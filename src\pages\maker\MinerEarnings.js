import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Badge, Form, InputGroup, Pagination } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const MinerEarnings = () => {
    const { t } = useTranslation();
    const [earnings, setEarnings] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [filteredEarnings, setFilteredEarnings] = useState([]);

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [earningsPerPage] = useState(10);
    const [paginatedEarnings, setPaginatedEarnings] = useState([]);

    useEffect(() => {
        const fetchEarnings = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch miner daily earnings with miner information
            const { data, error } = await supabase
                .from('miner_daily_earnings')
                .select(`
                    miner_id,
                    earn_date,
                    cumulative_reward,
                    daily_reward,
                    blocks_won,
                    created_at,
                    miners (
                        filecoin_miner_id,
                        category,
                        facilities (
                            name
                        )
                    )
                `)
                .order('earn_date', { ascending: false });

            if (error) {
                console.error('Error fetching miner earnings:', error);
            } else {
                setEarnings(data);
            }
            setLoading(false);
        };

        fetchEarnings();
    }, []);

    // Filter earnings based on search criteria
    useEffect(() => {
        let filtered = earnings;

        // Search by miner ID
        if (searchTerm) {
            filtered = filtered.filter(earning =>
                earning.miners?.filecoin_miner_id?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                earning.miners?.facilities?.name?.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Filter by date range
        if (startDate) {
            filtered = filtered.filter(earning =>
                new Date(earning.earn_date) >= new Date(startDate)
            );
        }
        if (endDate) {
            filtered = filtered.filter(earning =>
                new Date(earning.earn_date) <= new Date(endDate)
            );
        }

        setFilteredEarnings(filtered);
        setCurrentPage(1); // Reset to first page when filters change
    }, [earnings, searchTerm, startDate, endDate]);

    // Paginate filtered earnings
    useEffect(() => {
        const indexOfLastEarning = currentPage * earningsPerPage;
        const indexOfFirstEarning = indexOfLastEarning - earningsPerPage;
        const currentEarnings = filteredEarnings.slice(indexOfFirstEarning, indexOfLastEarning);
        setPaginatedEarnings(currentEarnings);
    }, [filteredEarnings, currentPage, earningsPerPage]);

    // Pagination handlers
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const totalPages = Math.ceil(filteredEarnings.length / earningsPerPage);

    if (loading) {
        return <div>{t('loading_earnings')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('miner_earnings')}</h2>

            {/* Search and Filter Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={4}>
                                    <Form.Group>
                                        <Form.Label>{t('search_miner')}</Form.Label>
                                        <InputGroup>
                                            <Form.Control
                                                type="text"
                                                placeholder={t('search_miner_id_or_facility')}
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                            />
                                        </InputGroup>
                                    </Form.Group>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('start_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('end_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Earnings Table */}
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('miner_id')}</th>
                                        <th>{t('facility')}</th>
                                        <th>{t('earn_date')}</th>
                                        <th>{t('daily_reward')}</th>
                                        <th>{t('cumulative_reward')}</th>
                                        <th>{t('blocks_won')}</th>
                                        <th>{t('created_at')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedEarnings.length === 0 ? (
                                        <tr>
                                            <td colSpan="7" className="text-center">{t('no_earnings_available')}</td>
                                        </tr>
                                    ) : (
                                        paginatedEarnings.map((earning) => (
                                            <tr key={`${earning.miner_id}-${earning.earn_date}`}>
                                                <td>{earning.miners?.filecoin_miner_id || '-'}</td>
                                                <td>{earning.miners?.facilities?.name || '-'}</td>
                                                <td>{new Date(earning.earn_date).toLocaleDateString()}</td>
                                                <td>{earning.daily_reward ? Number(earning.daily_reward).toFixed(6) : '0'} FIL</td>
                                                <td>{earning.cumulative_reward ? Number(earning.cumulative_reward).toFixed(6) : '0'} FIL</td>
                                                <td>{earning.blocks_won || 0}</td>
                                                <td>{new Date(earning.created_at).toLocaleString()}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />

                                        {/* Show page numbers */}
                                        {[...Array(totalPages)].map((_, index) => {
                                            const pageNumber = index + 1;
                                            // Show first page, last page, current page, and pages around current page
                                            if (
                                                pageNumber === 1 ||
                                                pageNumber === totalPages ||
                                                (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
                                            ) {
                                                return (
                                                    <Pagination.Item
                                                        key={pageNumber}
                                                        active={pageNumber === currentPage}
                                                        onClick={() => handlePageChange(pageNumber)}
                                                    >
                                                        {pageNumber}
                                                    </Pagination.Item>
                                                );
                                            } else if (
                                                pageNumber === currentPage - 3 ||
                                                pageNumber === currentPage + 3
                                            ) {
                                                return <Pagination.Ellipsis key={pageNumber} />;
                                            }
                                            return null;
                                        })}

                                        <Pagination.Next
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default MinerEarnings;