import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Form, InputGroup, Modal, Alert, Pagination } from 'react-bootstrap';
import { FaSearch, FaCheck, FaTimes, FaEye, FaDownload } from 'react-icons/fa';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import StatusBadge from '../../components/StatusBadge';

const WithdrawList = () => {
    const { t } = useTranslation();
    const [withdrawals, setWithdrawals] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [withdrawalsPerPage] = useState(10);
    const [paginatedWithdrawals, setPaginatedWithdrawals] = useState([]);
    const [filteredWithdrawals, setFilteredWithdrawals] = useState([]);

    // Withdrawal review modal states
    const [showReviewModal, setShowReviewModal] = useState(false);
    const [selectedWithdrawal, setSelectedWithdrawal] = useState(null);
    const [reviewLoading, setReviewLoading] = useState(false);
    const [reviewError, setReviewError] = useState('');
    const [reviewSuccess, setReviewSuccess] = useState('');

    useEffect(() => {
        fetchWithdrawals();
    }, []);

    // Filter withdrawals based on search criteria
    useEffect(() => {
        const filtered = withdrawals.filter(withdrawal => {
            const matchesSearch = !searchTerm ||
                withdrawal.customer_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                withdrawal.customer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                withdrawal.address?.toLowerCase().includes(searchTerm.toLowerCase());

            const matchesStatus = !statusFilter || withdrawal.status === statusFilter;

            const matchesDateRange = (!startDate || new Date(withdrawal.requested_at) >= new Date(startDate)) &&
                                   (!endDate || new Date(withdrawal.requested_at) <= new Date(endDate));

            return matchesSearch && matchesStatus && matchesDateRange;
        });

        setFilteredWithdrawals(filtered);
        setCurrentPage(1); // Reset to first page when filters change
    }, [withdrawals, searchTerm, statusFilter, startDate, endDate]);

    // Paginate filtered withdrawals
    useEffect(() => {
        const indexOfLastWithdrawal = currentPage * withdrawalsPerPage;
        const indexOfFirstWithdrawal = indexOfLastWithdrawal - withdrawalsPerPage;
        const currentWithdrawals = filteredWithdrawals.slice(indexOfFirstWithdrawal, indexOfLastWithdrawal);
        setPaginatedWithdrawals(currentWithdrawals);
    }, [filteredWithdrawals, currentPage, withdrawalsPerPage]);

    // Pagination handlers
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const totalPages = Math.ceil(filteredWithdrawals.length / withdrawalsPerPage);

    // Export filtered withdrawals to CSV
    const exportToCSV = () => {
        if (filteredWithdrawals.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('withdrawal_id'),
            t('customer_email'),
            t('customer_name'),
            t('currency_code'),
            t('request_amount'),
            t('final_amount'),
            t('fee'),
            t('address'),
            t('status'),
            t('user_remark'),
            t('requested_at'),
            t('reviewed_at')
        ];

        // Convert data to CSV format
        const csvData = filteredWithdrawals.map(withdrawal => [
            withdrawal.id,
            withdrawal.customer_email || '-',
            withdrawal.customer_name || '-',
            withdrawal.currency_code || '-',
            withdrawal.request_amount?.toFixed(6) || '0.000000',
            withdrawal.final_amount?.toFixed(6) || '0.000000',
            withdrawal.fee?.toFixed(6) || '0.000000',
            withdrawal.address || '-',
            t(withdrawal.status) || 'pending',
            withdrawal.user_remark || '-',
            new Date(withdrawal.requested_at).toLocaleString(),
            withdrawal.reviewed_at ? new Date(withdrawal.reviewed_at).toLocaleString() : '-'
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `agent_withdrawals_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const fetchWithdrawals = async () => {
        const supabase = getSupabase();
        if (!supabase) return;

        setLoading(true);
        const { data: { user } } = await supabase.auth.getUser();

        if (!user) {
            setLoading(false);
            return;
        }

        try {
            // First get the agent profile for the current user
            const { data: agentProfile, error: agentError } = await supabase
                .from('agent_profiles')
                .select('user_id')
                .eq('user_id', user.id)
                .single();

            if (agentError || !agentProfile) {
                console.error('Error fetching agent profile:', agentError);
                setLoading(false);
                return;
            }

            // Get all customers under this agent
            const { data: customerProfiles, error: customerError } = await supabase
                .from('customer_profiles')
                .select(`
                    user_id,
                    real_name,
                    users (
                        email
                    )
                `)
                .eq('agent_id', agentProfile.user_id);

            if (customerError) {
                console.error('Error fetching customer profiles:', customerError);
                setLoading(false);
                return;
            }

            const customerUserIds = customerProfiles.map(cp => cp.user_id).filter(Boolean);

            if (customerUserIds.length === 0) {
                setWithdrawals([]);
                setLoading(false);
                return;
            }

            // Fetch withdrawals for these customers using batch queries to avoid URL length issues
            const fetchWithdrawalsBatch = async (userIds, batchSize = 100) => {
                const batches = [];
                for (let i = 0; i < userIds.length; i += batchSize) {
                    batches.push(userIds.slice(i, i + batchSize));
                }

                const allWithdrawals = [];
                for (const batch of batches) {
                    try {
                        const { data: batchData, error: batchError } = await supabase
                            .from('withdrawals')
                            .select(`
                                id,
                                user_id,
                                currency_code,
                                request_amount,
                                final_amount,
                                fee,
                                user_remark,
                                admin_remark,
                                status,
                                requested_at,
                                reviewed_at,
                                wallet_type,
                                address,
                                users (
                                    email
                                )
                            `)
                            .in('user_id', batch)
                            .order('requested_at', { ascending: false });

                        if (batchError) {
                            console.error('Error fetching withdrawals batch:', batchError);
                            continue; // Continue with other batches
                        }

                        if (batchData) {
                            allWithdrawals.push(...batchData);
                        }
                    } catch (error) {
                        console.error('Error in withdrawals batch query:', error);
                        continue; // Continue with other batches
                    }
                }

                // Sort all withdrawals by requested_at descending
                return allWithdrawals.sort((a, b) => new Date(b.requested_at) - new Date(a.requested_at));
            };

            const data = await fetchWithdrawalsBatch(customerUserIds);

            if (data && data.length > 0) {
                // Merge customer profile data
                const enrichedWithdrawals = data.map(withdrawal => {
                    const customerProfile = customerProfiles.find(cp => cp.user_id === withdrawal.user_id);
                    return {
                        ...withdrawal,
                        customer_name: customerProfile?.real_name || '-',
                        customer_email: customerProfile?.users?.email || withdrawal.users?.email || '-'
                    };
                });
                setWithdrawals(enrichedWithdrawals);
            } else {
                setWithdrawals([]);
            }
        } catch (error) {
            console.error('Error in fetchWithdrawals:', error);
        }
        setLoading(false);
    };



    const handleSearch = () => {
        fetchWithdrawals();
    };

    const handleWithdrawalReview = (withdrawal) => {
        setSelectedWithdrawal(withdrawal);
        setShowReviewModal(true);
        setReviewError('');
        setReviewSuccess('');
    };

    const handleWithdrawalDecision = async (decision) => {
        if (!selectedWithdrawal) return;

        setReviewLoading(true);
        setReviewError('');
        setReviewSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Get current user for audit log
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                throw new Error('User not authenticated');
            }

            // Get the old status before updating
            const { data: oldData, error: selectError } = await supabase
                .from('withdrawals')
                .select('status')
                .eq('id', selectedWithdrawal.id)
                .single();

            if (selectError) {
                console.error('Error fetching old status:', selectError);
                throw selectError;
            }

            const oldStatus = oldData?.status || 'pending';

            let oldUserAssets = null;
            let newUserAssets = null;

            // If approving, update user_assets
            if (decision === 'approved') {

                // Get current user assets
                const { data: currentAssets, error: assetsSelectError } = await supabase
                    .from('user_assets')
                    .select('balance_available, balance_total, withdrawn_total')
                    .eq('user_id', selectedWithdrawal.user_id)
                    .eq('currency_code', selectedWithdrawal.currency_code);

                if (assetsSelectError) {
                    console.error('Error fetching user assets:', assetsSelectError);
                    throw new Error(`Failed to fetch user assets: ${assetsSelectError.message}`);
                }

                // Check if user assets exist, if not create them
                if (!currentAssets || currentAssets.length === 0) {
                    console.log('No user assets found, creating default record for user:', selectedWithdrawal.user_id);

                    // Create default user_assets record
                    const { data: newAssets, error: createError } = await supabase
                        .from('user_assets')
                        .insert({
                            user_id: selectedWithdrawal.user_id,
                            currency_code: selectedWithdrawal.currency_code,
                            balance_available: 0,
                            balance_locked: 0,
                            balance_total: 0,
                            withdrawn_total: 0
                        })
                        .select();

                    if (createError) {
                        console.error('Error creating user assets:', createError);
                        throw new Error(`Failed to create user assets: ${createError.message}`);
                    }

                    if (!newAssets || newAssets.length === 0) {
                        throw new Error('Failed to create user assets record');
                    }

                    // Since we just created a record with 0 balance, user cannot withdraw
                    throw new Error(`User has insufficient balance (0 ${selectedWithdrawal.currency_code}) for withdrawal of ${selectedWithdrawal.final_amount}`);
                }

                const userAsset = currentAssets[0]; // Get the first (and should be only) record

                oldUserAssets = {
                    balance_available: userAsset.balance_available,
                    balance_total: userAsset.balance_total,
                    withdrawn_total: userAsset.withdrawn_total
                };

                const withdrawAmount = parseFloat(selectedWithdrawal.final_amount);

                // Validate sufficient balance
                if (parseFloat(userAsset.balance_available) < withdrawAmount) {
                    throw new Error(`Insufficient balance. Available: ${userAsset.balance_available}, Requested: ${withdrawAmount}`);
                }

                // Calculate new balances
                const newBalanceAvailable = parseFloat(userAsset.balance_available) - withdrawAmount;
                const newBalanceTotal = parseFloat(userAsset.balance_total) - withdrawAmount;
                const newWithdrawnTotal = parseFloat(userAsset.withdrawn_total) + withdrawAmount;

                // Update user_assets
                const { data: updatedAssets, error: assetsUpdateError } = await supabase
                    .from('user_assets')
                    .update({
                        balance_available: newBalanceAvailable,
                        balance_total: newBalanceTotal,
                        withdrawn_total: newWithdrawnTotal
                    })
                    .eq('user_id', selectedWithdrawal.user_id)
                    .eq('currency_code', selectedWithdrawal.currency_code)
                    .select();

                console.log('User assets update result:', { updatedAssets, assetsUpdateError });

                if (assetsUpdateError) {
                    console.error('Error updating user assets:', assetsUpdateError);
                    throw new Error(`Failed to update user assets: ${assetsUpdateError.message}`);
                }

                if (!updatedAssets || updatedAssets.length === 0) {
                    throw new Error('No rows were updated in user_assets table. The record may not exist.');
                }

                newUserAssets = {
                    balance_available: newBalanceAvailable,
                    balance_total: newBalanceTotal,
                    withdrawn_total: newWithdrawnTotal
                };
            }

            // Update the withdrawal status and reviewed_at timestamp
            const { data, error } = await supabase
                .from('withdrawals')
                .update({
                    status: decision,
                    reviewed_at: new Date().toISOString()
                })
                .eq('id', selectedWithdrawal.id)
                .select();

            if (error) {
                console.error('Database error:', error);
                throw error;
            }

            // Check if any rows were updated
            if (!data || data.length === 0) {
                throw new Error('Withdrawal record not found or no changes made');
            }

            // Prepare audit log data
            const auditLogData = {
                user_id: user.id, // Current user (agent) who made the decision
                action: decision,
                object_table: 'withdrawals',
                object_id: selectedWithdrawal.id, // The withdrawal that was reviewed
                diff: {
                    old: { status: oldStatus },
                    new: { status: decision }
                }
            };

            // Add user_assets changes to audit log if withdrawal was approved
            if (decision === 'approved' && oldUserAssets && newUserAssets) {
                auditLogData.diff.old.user_assets = {
                    balance_total: oldUserAssets.balance_total
                };
                auditLogData.diff.new.user_assets = {
                    balance_total: newUserAssets.balance_total
                };
            }

            // First create audit log record and get its ID
            const { data: auditLogData_result, error: auditError } = await supabase
                .from('audit_logs')
                .insert(auditLogData)
                .select();

            if (auditError) {
                console.error('Error inserting audit log:', auditError);
                throw auditError; // Throw error since we need the audit log ID for transaction
            }

            const auditLogId = auditLogData_result[0].id;

            // Insert transaction record for approved withdrawals with audit_id
            if (decision === 'approved') {
                const { data: transactionData, error: transactionError } = await supabase
                    .from('transactions')
                    .insert({
                        sender_user_id: selectedWithdrawal.user_id,
                        receiver_user_id: null, // 对外转出，无接收方
                        amount_net: parseFloat(selectedWithdrawal.final_amount),
                        tx_type: 'withdraw',
                        filecoin_msg_id: selectedWithdrawal.address, // 申请的address
                        agent_id: user.id, // 当前用户的id
                        audit_id: auditLogId // 关联的audit_logs记录ID
                    })
                    .select();

                if (transactionError) {
                    console.error('Error inserting transaction record:', transactionError);
                    // Don't throw error here as the main operations succeeded
                    // Just log the error for debugging
                }
            }

            // Update local state
            setWithdrawals(prevWithdrawals =>
                prevWithdrawals.map(withdrawal =>
                    withdrawal.id === selectedWithdrawal.id
                        ? { ...withdrawal, status: decision, reviewed_at: new Date().toISOString() }
                        : withdrawal
                )
            );

            // Also update the selected withdrawal
            setSelectedWithdrawal(prev => ({
                ...prev,
                status: decision,
                reviewed_at: new Date().toISOString()
            }));

            setReviewSuccess(decision === 'approved' ? t('withdrawal_approved_success') : t('withdrawal_rejected_success'));

            // Close modal after 1.5 seconds
            setTimeout(() => {
                setShowReviewModal(false);
                setSelectedWithdrawal(null);
            }, 1500);

        } catch (error) {
            console.error('Error updating withdrawal status:', error);
            setReviewError(error.message || t('withdrawal_review_error'));
        } finally {
            setReviewLoading(false);
        }
    };

    const closeReviewModal = () => {
        setShowReviewModal(false);
        setSelectedWithdrawal(null);
        setReviewError('');
        setReviewSuccess('');
    };



    if (loading) {
        return <div>{t('loading')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('withdraw_list')}</h2>

            {/* Search and Filter Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('export_data')}</Form.Label>
                                        <div>
                                            <Button
                                                variant="success"
                                                onClick={exportToCSV}
                                                className="mb-2"
                                                disabled={filteredWithdrawals.length === 0}
                                            >
                                                <FaDownload className="me-1" />
                                                {t('export_all')}
                                            </Button>
                                        </div>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('search')}</Form.Label>
                                        <InputGroup>
                                            <Form.Control
                                                type="text"
                                                placeholder={t('search_by_email_or_address')}
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                            />
                                        </InputGroup>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('status_filter')}</Form.Label>
                                        <Form.Select
                                            value={statusFilter}
                                            onChange={(e) => setStatusFilter(e.target.value)}
                                        >
                                            <option value="">{t('all_status')}</option>
                                            <option value="pending">{t('pending_review')}</option>
                                            <option value="approved">{t('approved')}</option>
                                            <option value="rejected">{t('rejected')}</option>
                                            <option value="processing">{t('processing')}</option>
                                            <option value="completed">{t('completed')}</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('start_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('end_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={1}>
                                    <Form.Group>
                                        <Button variant="primary" onClick={handleSearch}>
                                            <FaSearch />
                                        </Button>
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('withdraw_id')}</th>
                                        <th>{t('customer')}</th>
                                        <th>{t('request_amount')}</th>
                                        <th>{t('final_amount')}</th>
                                        <th>{t('fee')}</th>
                                        <th>{t('address')}</th>
                                        <th>{t('status')}</th>
                                        <th>{t('requested_at')}</th>
                                        <th>{t('reviewed_at')}</th>
                                        <th>{t('actions')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedWithdrawals.length === 0 ? (
                                        <tr>
                                            <td colSpan="12" className="text-center">{t('no_withdrawals_available')}</td>
                                        </tr>
                                    ) : (
                                        paginatedWithdrawals.map(withdrawal => (
                                            <tr key={withdrawal.id}>
                                                <td>{withdrawal.id.substring(0, 8)}...</td>
                                                <td>
                                                    <div>
                                                        <div>{withdrawal.customer_name}</div>
                                                        <small className="text-muted">
                                                            {withdrawal.customer_email}
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>{withdrawal.request_amount?.toFixed(6) || '0.000000'}</td>
                                                <td>{withdrawal.final_amount?.toFixed(6) || '0.000000'}</td>
                                                <td>{withdrawal.fee?.toFixed(6) || '0.000000'}</td>
                                                <td>
                                                    <span title={withdrawal.address}>
                                                        {withdrawal.address ?
                                                            `${withdrawal.address.substring(0, 10)}...${withdrawal.address.substring(withdrawal.address.length - 6)}`
                                                            : '-'
                                                        }
                                                    </span>
                                                </td>
                                                <td><StatusBadge status={withdrawal.status} type="withdrawal" /></td>
                                                <td>{new Date(withdrawal.requested_at).toLocaleString()}</td>
                                                <td>{withdrawal.reviewed_at ? new Date(withdrawal.reviewed_at).toLocaleString() : '-'}</td>
                                                <td>
                                                    <Button
                                                        size="sm"
                                                        variant="outline-primary"
                                                        onClick={() => handleWithdrawalReview(withdrawal)}
                                                        title={t('withdrawal_review')}
                                                        disabled={withdrawal.status === 'approved' || withdrawal.status === 'rejected'}
                                                    >
                                                        <FaEye />
                                                    </Button>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />

                                        {/* Show page numbers */}
                                        {[...Array(totalPages)].map((_, index) => {
                                            const pageNumber = index + 1;
                                            // Show first page, last page, current page, and pages around current page
                                            if (
                                                pageNumber === 1 ||
                                                pageNumber === totalPages ||
                                                (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
                                            ) {
                                                return (
                                                    <Pagination.Item
                                                        key={pageNumber}
                                                        active={pageNumber === currentPage}
                                                        onClick={() => handlePageChange(pageNumber)}
                                                    >
                                                        {pageNumber}
                                                    </Pagination.Item>
                                                );
                                            } else if (
                                                pageNumber === currentPage - 3 ||
                                                pageNumber === currentPage + 3
                                            ) {
                                                return <Pagination.Ellipsis key={pageNumber} />;
                                            }
                                            return null;
                                        })}

                                        <Pagination.Next
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Withdrawal Review Modal */}
            <Modal show={showReviewModal} onHide={closeReviewModal} size="lg">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('withdrawal_review')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeReviewModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {reviewError && <Alert variant="danger">{reviewError}</Alert>}
                    {reviewSuccess && <Alert variant="success">{reviewSuccess}</Alert>}

                    {selectedWithdrawal && (
                        <>
                            <Row className="mb-3">
                                <Col md={6}>
                                    <strong>{t('withdraw_id')}:</strong> {selectedWithdrawal.id}
                                </Col>
                                <Col md={6}>
                                    <strong>{t('customer')}:</strong> {selectedWithdrawal.customer_name} ({selectedWithdrawal.customer_email})
                                </Col>
                            </Row>
                            <Row className="mb-3">
                                <Col md={6}>
                                    <strong>{t('currency')}:</strong> {selectedWithdrawal.currency_code}
                                </Col>
                                <Col md={6}>
                                    <strong>{t('request_amount')}:</strong> {selectedWithdrawal.request_amount?.toFixed(6) || '0.000000'}
                                </Col>
                            </Row>
                            <Row className="mb-3">
                                <Col md={6}>
                                    <strong>{t('final_amount')}:</strong> {selectedWithdrawal.final_amount?.toFixed(6) || '0.000000'}
                                </Col>
                                <Col md={6}>
                                    <strong>{t('fee')}:</strong> {selectedWithdrawal.fee?.toFixed(6) || '0.000000'}
                                </Col>
                            </Row>
                            <Row className="mb-3">
                                <Col md={6}>
                                    <strong>{t('wallet_type')}:</strong> {selectedWithdrawal.wallet_type || '-'}
                                </Col>
                                <Col md={6}>
                                    <strong>{t('status')}:</strong> <StatusBadge status={selectedWithdrawal.status} type="withdrawal" />
                                </Col>
                            </Row>
                            <Row className="mb-3">
                                <Col md={12}>
                                    <strong>{t('address')}:</strong>
                                    <div className="mt-1" style={{ wordBreak: 'break-all' }}>
                                        {selectedWithdrawal.address || '-'}
                                    </div>
                                </Col>
                            </Row>
                            <Row className="mb-3">
                                <Col md={6}>
                                    <strong>{t('requested_at')}:</strong> {new Date(selectedWithdrawal.requested_at).toLocaleString()}
                                </Col>
                                <Col md={6}>
                                    <strong>{t('reviewed_at')}:</strong> {selectedWithdrawal.reviewed_at ? new Date(selectedWithdrawal.reviewed_at).toLocaleString() : '-'}
                                </Col>
                            </Row>
                            {selectedWithdrawal.user_remark && (
                                <Row className="mb-3">
                                    <Col md={12}>
                                        <strong>{t('user_remark')}:</strong>
                                        <div className="mt-1">{selectedWithdrawal.user_remark}</div>
                                    </Col>
                                </Row>
                            )}
                            {selectedWithdrawal.admin_remark && (
                                <Row className="mb-3">
                                    <Col md={12}>
                                        <strong>{t('admin_remark')}:</strong>
                                        <div className="mt-1">{selectedWithdrawal.admin_remark}</div>
                                    </Col>
                                </Row>
                            )}
                        </>
                    )}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeReviewModal} disabled={reviewLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="danger"
                        onClick={() => handleWithdrawalDecision('rejected')}
                        disabled={reviewLoading || selectedWithdrawal?.status === 'rejected'}
                    >
                        {reviewLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('processing')}
                            </>
                        ) : (
                            <>
                                <FaTimes className="me-1" />
                                {t('reject')}
                            </>
                        )}
                    </Button>
                    <Button
                        variant="success"
                        onClick={() => handleWithdrawalDecision('approved')}
                        disabled={reviewLoading || selectedWithdrawal?.status === 'approved'}
                    >
                        {reviewLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('processing')}
                            </>
                        ) : (
                            <>
                                <FaCheck className="me-1" />
                                {t('approve')}
                            </>
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default WithdrawList;
