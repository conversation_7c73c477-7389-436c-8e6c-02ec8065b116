import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Modal, Form, Alert } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import { FaDownload } from "react-icons/fa";
import StatusBadge from '../../components/StatusBadge';

const CapacityRequest = () => {
    const { t } = useTranslation();
    const [requests, setRequests] = useState([]);
    const [loading, setLoading] = useState(true);

    // Review Modal states
    const [showReviewModal, setShowReviewModal] = useState(false);
    const [reviewLoading, setReviewLoading] = useState(false);
    const [reviewError, setReviewError] = useState('');
    const [reviewSuccess, setReviewSuccess] = useState('');
    const [reviewingRequest, setReviewingRequest] = useState(null);
    const [reviewDecision, setReviewDecision] = useState('');
    const [reviewReply, setReviewReply] = useState('');

    useEffect(() => {
        const fetchRequests = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // Fetch capacity requests with maker and requester information
            const { data, error } = await supabase
                .from('capacity_requests')
                .select(`
                    id,
                    product_id,
                    added_capacity,
                    capacity_before,
                    capacity_after,
                    status,
                    description,
                    review_reply,
                    requested_at,
                    reviewed_at,
                    products (
                        name
                    ),
                    maker_profiles (
                        domain,
                        users (
                            email
                        )
                    ),
                    users (
                        email,
                        role
                    )
                `)
                .order('requested_at', { ascending: false });

            if (error) {
                console.error('Error fetching capacity requests:', error);
            } else {
                setRequests(data);
            }
            setLoading(false);
        };

        fetchRequests();
    }, []);

    // Export requests to CSV
    const exportToCSV = () => {
        if (requests.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('request_id'),
            t('maker'),
            t('requested_by'),
            t('product_name'),
            t('added_capacity'),
            t('capacity_before'),
            t('capacity_after'),
            t('status'),
            t('description'),
            t('review_reply'),
            t('requested_at'),
            t('reviewed_at')
        ];

        // Convert data to CSV format
        const csvData = requests.map(request => [
            request.id,
            request.maker_profiles?.domain || '-',
            request.users?.email || '-',
            request.products?.name || '-',
            request.added_capacity?.toFixed(2) || '0.00',
            request.capacity_before?.toFixed(2) || '0.00',
            request.capacity_after?.toFixed(2) || '0.00',
            t(request.status) || 'pending',
            request.description || '-',
            request.review_reply || '-',
            new Date(request.requested_at).toLocaleString(),
            request.reviewed_at ? new Date(request.reviewed_at).toLocaleString() : '-'
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `maker_capacity_requests_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleReview = (request) => {
        setReviewingRequest(request);
        setReviewDecision('');
        setReviewReply('');
        setReviewError('');
        setReviewSuccess('');
        setShowReviewModal(true);
    };

    const handleSubmitReview = async () => {
        if (!reviewDecision) {
            setReviewError(t('please_select_decision'));
            return;
        }

        setReviewLoading(true);
        setReviewError('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Get current user
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                throw new Error('User not authenticated');
            }

            // Update capacity request status
            const { error: updateError } = await supabase
                .from('capacity_requests')
                .update({
                    status: reviewDecision,
                    review_reply: reviewReply,
                    reviewed_at: new Date().toISOString()
                })
                .eq('id', reviewingRequest.id);

            if (updateError) {
                throw updateError;
            }

            // If approved, update product total_shares and create audit log
            if (reviewDecision === 'approved') {
                // Get current product data
                const { data: productData, error: productError } = await supabase
                    .from('products')
                    .select('total_shares')
                    .eq('id', reviewingRequest.product_id)
                    .single();

                if (productError) {
                    throw new Error('Failed to fetch product data');
                }

                const oldTotalShares = productData.total_shares;
                const newTotalShares = oldTotalShares + reviewingRequest.added_capacity;

                // Update product total_shares
                const { error: productUpdateError } = await supabase
                    .from('products')
                    .update({ total_shares: newTotalShares })
                    .eq('id', reviewingRequest.product_id);

                if (productUpdateError) {
                    throw productUpdateError;
                }

                // Insert audit log
                const auditLogData = {
                    user_id: user.id,
                    action: 'capacity_approved',
                    object_table: 'products',
                    object_id: reviewingRequest.product_id,
                    diff: {
                        old: { total_shares: oldTotalShares },
                        new: { total_shares: newTotalShares },
                        capacity_request_id: reviewingRequest.id,
                        added_capacity: reviewingRequest.added_capacity
                    }
                };

                const { error: auditError } = await supabase
                    .from('audit_logs')
                    .insert(auditLogData);

                if (auditError) {
                    console.error('Error inserting audit log:', auditError);
                    // Don't throw error here as the main operation succeeded
                }
            }

            setReviewSuccess(t('review_submitted_successfully'));

            // Refresh requests
            const { data, error } = await supabase
                .from('capacity_requests')
                .select(`
                    id,
                    product_id,
                    added_capacity,
                    capacity_before,
                    capacity_after,
                    status,
                    description,
                    review_reply,
                    requested_at,
                    reviewed_at,
                    products (
                        name
                    ),
                    maker_profiles (
                        domain,
                        users (
                            email
                        )
                    ),
                    users (
                        email,
                        role
                    )
                `)
                .order('requested_at', { ascending: false });

            if (!error) {
                setRequests(data);
            }

            // Close modal after 2 seconds
            setTimeout(() => {
                setShowReviewModal(false);
            }, 2000);

        } catch (error) {
            console.error('Error submitting review:', error);
            setReviewError(error.message || t('review_submission_error'));
        } finally {
            setReviewLoading(false);
        }
    };

    const closeReviewModal = () => {
        setShowReviewModal(false);
        setReviewingRequest(null);
        setReviewDecision('');
        setReviewReply('');
        setReviewError('');
        setReviewSuccess('');
    };

    if (loading) {
        return <div>{t('loading_capacity_requests')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('capacity_expansion_request')}</h2>

            {/* Top Operation Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('export_data')}</Form.Label>
                                        <div>
                                            <Button
                                                variant="success"
                                                onClick={exportToCSV}
                                                className="mb-2"
                                                disabled={requests.length === 0}
                                            >
                                                <FaDownload className="me-1" />
                                                {t('export_all')}
                                            </Button>
                                        </div>
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('request_id')}</th>
                                        <th>{t('maker')}</th>
                                        <th>{t('requested_by')}</th>
                                        <th>{t('product_name')}</th>
                                        <th>{t('added_capacity')}</th>
                                        <th>{t('capacity_before')}</th>
                                        <th>{t('capacity_after')}</th>
                                        <th>{t('status')}</th>
                                        <th>{t('description')}</th>
                                        <th>{t('review_reply')}</th>
                                        <th>{t('requested_at')}</th>
                                        <th>{t('reviewed_at')}</th>
                                        <th>{t('actions')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {requests.length === 0 ? (
                                        <tr>
                                            <td colSpan="13" className="text-center">{t('no_capacity_requests_available')}</td>
                                        </tr>
                                    ) : (
                                        requests.map(request => (
                                            <tr key={request.id}>
                                                <td>{request.id}</td>
                                                <td>
                                                    <div>
                                                        <div>{request.maker_profiles?.domain || '-'}</div>
                                                        <small className="text-muted">
                                                            {request.maker_profiles?.users?.email || '-'}
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>{request.users?.email || '-'}</td>
                                                <td>{request.products?.name || '-'}</td>
                                                <td>{request.added_capacity?.toFixed(2) || '0.00'}</td>
                                                <td>{request.capacity_before?.toFixed(2) || '0.00'}</td>
                                                <td>{request.capacity_after?.toFixed(2) || '0.00'}</td>
                                                <td><StatusBadge status={request.status} type="review" /></td>
                                                <td>
                                                    <div style={{ maxWidth: '200px', wordWrap: 'break-word' }}>
                                                        {request.description || '-'}
                                                    </div>
                                                </td>
                                                <td>
                                                    <div style={{ maxWidth: '200px', wordWrap: 'break-word' }}>
                                                        {request.review_reply || '-'}
                                                    </div>
                                                </td>
                                                <td>{new Date(request.requested_at).toLocaleString()}</td>
                                                <td>{request.reviewed_at ? new Date(request.reviewed_at).toLocaleString() : '-'}</td>
                                                <td>
                                                    {request.status === 'pending' && (
                                                        <Button
                                                            variant="primary"
                                                            size="sm"
                                                            onClick={() => handleReview(request)}
                                                        >
                                                            {t('review')}
                                                        </Button>
                                                    )}
                                                    {request.status !== 'pending' && (
                                                        <span className="text-muted">{t('reviewed')}</span>
                                                    )}
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Review Modal */}
            <Modal show={showReviewModal} onHide={closeReviewModal} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>{t('review_capacity_request')}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    {reviewError && <Alert variant="danger">{reviewError}</Alert>}
                    {reviewSuccess && <Alert variant="success">{reviewSuccess}</Alert>}

                    {reviewingRequest && (
                        <div className="mb-3">
                            <h6>{t('request_details')}</h6>
                            <p><strong>{t('product_name')}:</strong> {reviewingRequest.products?.name}</p>
                            <p><strong>{t('requested_by')}:</strong> {reviewingRequest.users?.email}</p>
                            <p><strong>{t('added_capacity')}:</strong> {reviewingRequest.added_capacity}</p>
                            <p><strong>{t('capacity_before')}:</strong> {reviewingRequest.capacity_before}</p>
                            <p><strong>{t('capacity_after')}:</strong> {reviewingRequest.capacity_after}</p>
                            <p><strong>{t('description')}:</strong> {reviewingRequest.description || '-'}</p>
                        </div>
                    )}

                    <Form>
                        <Form.Group className="mb-3">
                            <Form.Label>{t('decision')} *</Form.Label>
                            <Form.Select
                                value={reviewDecision}
                                onChange={(e) => setReviewDecision(e.target.value)}
                                required
                            >
                                <option value="">{t('select_decision')}</option>
                                <option value="approved">{t('approve')}</option>
                                <option value="rejected">{t('reject')}</option>
                            </Form.Select>
                        </Form.Group>

                        <Form.Group className="mb-3">
                            <Form.Label>{t('review_reply')}</Form.Label>
                            <Form.Control
                                as="textarea"
                                rows={3}
                                value={reviewReply}
                                onChange={(e) => setReviewReply(e.target.value)}
                                placeholder={t('enter_review_reply')}
                            />
                        </Form.Group>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeReviewModal}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleSubmitReview}
                        disabled={reviewLoading}
                    >
                        {reviewLoading ? t('submitting') : t('submit_review')}
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default CapacityRequest;
