import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, ProgressBar, Form, InputGroup, Button } from 'react-bootstrap';
import { FaDownload } from 'react-icons/fa';
import { getSupabase, getCurrentMakerId } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import StatusBadge from '../../components/StatusBadge';

const OrderDistributions = () => {
    const { t } = useTranslation();
    const [distributions, setDistributions] = useState([]);
    const [loading, setLoading] = useState(true);
    const [customerEmailFilter, setCustomerEmailFilter] = useState('');
    const [orderIdFilter, setOrderIdFilter] = useState('');
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [filteredDistributions, setFilteredDistributions] = useState([]);

    useEffect(() => {
        const fetchDistributions = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);

            // Get maker ID (works for both maker and technician roles)
            const makerId = await getCurrentMakerId();
            if (!makerId) {
                setLoading(false);
                return; // User not authenticated or not authorized
            }

            // Fetch order distributions with related information, filtered by maker_id
            const { data, error } = await supabase
                .from('order_distributions')
                .select(`
                    id,
                    share_amount,
                    reward_amount,
                    fee_amount,
                    progress,
                    created_at,
                    distribution_batches (
                        id,
                        currency_code,
                        batch_amount,
                        per_share_amount,
                        status,
                        distributed_at,
                        products (
                            name,
                            category,
                            maker_id
                        )
                    ),
                    orders (
                        id,
                        customer_id,
                        shares,
                        products (
                            name,
                            category,
                            maker_id
                        )
                    ),
                    customer_profiles (
                        real_name,
                        users (
                            email
                        )
                    )
                `)
                .filter('orders.products.maker_id', 'eq', makerId)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching order distributions:', error);
            } else {
                setDistributions(data);
            }
            setLoading(false);
        };

        fetchDistributions();
    }, []);

    // Filter distributions based on search criteria
    useEffect(() => {
        let filtered = distributions;

        // Filter by customer email
        if (customerEmailFilter) {
            filtered = filtered.filter(distribution =>
                distribution.customer_profiles?.users?.email?.toLowerCase().includes(customerEmailFilter.toLowerCase())
            );
        }

        // Filter by order ID
        if (orderIdFilter) {
            filtered = filtered.filter(distribution =>
                distribution.orders?.id?.toLowerCase().includes(orderIdFilter.toLowerCase()) ||
                distribution.orders?.customer_id?.toLowerCase().includes(orderIdFilter.toLowerCase())
            );
        }

        // Filter by date range
        if (startDate) {
            filtered = filtered.filter(distribution =>
                new Date(distribution.created_at) >= new Date(startDate)
            );
        }
        if (endDate) {
            filtered = filtered.filter(distribution =>
                new Date(distribution.created_at) <= new Date(endDate)
            );
        }

        setFilteredDistributions(filtered);
    }, [distributions, customerEmailFilter, orderIdFilter, startDate, endDate]);

    // Export filtered distributions to CSV
    const exportToCSV = () => {
        if (filteredDistributions.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('distribution_id'),
            t('batch_id'),
            t('order_id'),
            t('customer'),
            t('product_name'),
            t('currency_code'),
            t('share_amount'),
            t('reward_amount'),
            t('fee_amount'),
            t('progress'),
            t('batch_status'),
            t('created_at')
        ];

        // Convert data to CSV format
        const csvData = filteredDistributions.map(distribution => [
            distribution.id,
            distribution.distribution_batches?.id || '-',
            distribution.orders?.customer_id || distribution.orders?.id || '-',
            `${distribution.customer_profiles?.real_name || '-'} (${distribution.customer_profiles?.users?.email || '-'})`,
            distribution.orders?.products?.name || distribution.distribution_batches?.products?.name || '-',
            distribution.distribution_batches?.currency_code || '-',
            distribution.share_amount?.toFixed(2) || '0.00',
            distribution.reward_amount?.toFixed(6) || '0.000000',
            distribution.fee_amount?.toFixed(6) || '0.000000',
            `${(distribution.progress * 100).toFixed(1)}%`,
            distribution.distribution_batches?.status || '-',
            new Date(distribution.created_at).toLocaleString()
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `order_distributions_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    if (loading) {
        return <div>{t('loading_order_distributions')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('order_distributions')}</h2>

            {/* Top Operation Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('export_data')}</Form.Label>
                                        <div>
                                            <Button
                                                variant="success"
                                                onClick={exportToCSV}
                                                className="mb-2"
                                                disabled={filteredDistributions.length === 0}
                                            >
                                                <FaDownload className="me-1" />
                                                {t('export_all')}
                                            </Button>
                                        </div>
                                    </Form.Group>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('customer_email')}</Form.Label>
                                        <InputGroup>
                                            <Form.Control
                                                type="text"
                                                placeholder={t('search_by_customer_email')}
                                                value={customerEmailFilter}
                                                onChange={(e) => setCustomerEmailFilter(e.target.value)}
                                            />
                                        </InputGroup>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('order_id')}</Form.Label>
                                        <InputGroup>
                                            <Form.Control
                                                type="text"
                                                placeholder={t('search_by_order_id')}
                                                value={orderIdFilter}
                                                onChange={(e) => setOrderIdFilter(e.target.value)}
                                            />
                                        </InputGroup>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('start_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('end_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('distribution_id')}</th>
                                        <th>{t('batch_id')}</th>
                                        <th>{t('order_id')}</th>
                                        <th>{t('customer')}</th>
                                        <th>{t('product_name')}</th>
                                        <th>{t('currency_code')}</th>
                                        <th>{t('share_amount')}</th>
                                        <th>{t('reward_amount')}</th>
                                        <th>{t('fee_amount')}</th>
                                        <th>{t('progress')}</th>
                                        <th>{t('batch_status')}</th>
                                        <th>{t('created_at')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {filteredDistributions.length === 0 ? (
                                        <tr>
                                            <td colSpan="12" className="text-center">{t('no_order_distributions_available')}</td>
                                        </tr>
                                    ) : (
                                        filteredDistributions.map(distribution => (
                                            <tr key={distribution.id}>
                                                <td>{distribution.id}</td>
                                                <td>{distribution.distribution_batches?.id || '-'}</td>
                                                <td>{distribution.orders?.customer_id || distribution.orders?.id || '-'}</td>
                                                <td>
                                                    <div>
                                                        <div>{distribution.customer_profiles?.real_name || '-'}</div>
                                                        <small className="text-muted">
                                                            {distribution.customer_profiles?.users?.email || '-'}
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>
                                                    {distribution.orders?.products?.name || 
                                                     distribution.distribution_batches?.products?.name || '-'}
                                                </td>
                                                <td>{distribution.distribution_batches?.currency_code || '-'}</td>
                                                <td>{distribution.share_amount?.toFixed(2) || '0.00'}</td>
                                                <td>{distribution.reward_amount?.toFixed(6) || '0.000000'}</td>
                                                <td>{distribution.fee_amount?.toFixed(6) || '0.000000'}</td>
                                                <td>
                                                    <div>
                                                        <ProgressBar 
                                                            now={distribution.progress * 100} 
                                                            label={`${(distribution.progress * 100).toFixed(1)}%`}
                                                            style={{ minWidth: '100px' }}
                                                        />
                                                    </div>
                                                </td>
                                                <td><StatusBadge status={distribution.distribution_batches?.status} type="distribution" /></td>
                                                <td>{new Date(distribution.created_at).toLocaleString()}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default OrderDistributions;
