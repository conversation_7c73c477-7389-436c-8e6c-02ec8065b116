import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Form, InputGroup, Button, Modal, Alert } from 'react-bootstrap';
import { FaUserCheck, FaTimes } from 'react-icons/fa';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import StatusBadge from '../../components/StatusBadge';

const CancelledMembers = () => {
    const { t } = useTranslation();
    const [members, setMembers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [filteredMembers, setFilteredMembers] = useState([]);

    // Restore Member Modal states
    const [showRestoreMemberModal, setShowRestoreMemberModal] = useState(false);
    const [restoreMemberTarget, setRestoreMemberTarget] = useState(null);
    const [restoreMemberLoading, setRestoreMemberLoading] = useState(false);
    const [restoreMemberError, setRestoreMemberError] = useState('');
    const [restoreMemberSuccess, setRestoreMemberSuccess] = useState('');

    useEffect(() => {
        const fetchCancelledMembers = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return;
            }

            // Step 1: 查询 customer_profiles (只获取已取消的会员)
            const { data: customers, error: profileError } = await supabase
                .from('customer_profiles')
                .select('user_id, real_name, id_number, id_img_front, id_img_back, verify_status, cancelled')
                .eq('cancelled', true)
                .order('created_at', { ascending: false });

            if (profileError || !customers) {
                console.error('Error fetching cancelled customer_profiles:', profileError);
                setLoading(false);
                return;
            }

            // Step 2: 查询 users 表
            const userIds = customers.map(c => c.user_id).filter(Boolean);

            const { data: userInfoList, error: userError } = await supabase
                .from('users')
                .select('id, email, created_at')
                .in('id', userIds);

            if (userError) {
                console.error('Error fetching users:', userError);
            }

            // Step 3: 合并结果
            const usersMap = new Map((userInfoList || []).map(u => [u.id, u]));

            const enrichedMembers = customers.map(c => ({
                ...c,
                users: usersMap.get(c.user_id) || {}
            }));

            setMembers(enrichedMembers);
            setLoading(false);
        };

        fetchCancelledMembers();
    }, []);

    // Filter members based on search criteria
    useEffect(() => {
        let filtered = members;

        // Search by username (email)
        if (searchTerm) {
            filtered = filtered.filter(member =>
                member.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                member.real_name?.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Filter by status
        if (statusFilter) {
            filtered = filtered.filter(member => member.verify_status === statusFilter);
        }

        // Filter by date range
        if (startDate) {
            filtered = filtered.filter(member =>
                new Date(member.users?.created_at) >= new Date(startDate)
            );
        }
        if (endDate) {
            filtered = filtered.filter(member =>
                new Date(member.users?.created_at) <= new Date(endDate)
            );
        }

        setFilteredMembers(filtered);
    }, [members, searchTerm, statusFilter, startDate, endDate]);

    const handleRestoreMember = (member) => {
        setRestoreMemberTarget(member);
        setRestoreMemberError('');
        setRestoreMemberSuccess('');
        setShowRestoreMemberModal(true);
    };

    const handleConfirmRestoreMember = async () => {
        if (!restoreMemberTarget) return;

        setRestoreMemberLoading(true);
        setRestoreMemberError('');
        setRestoreMemberSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Update customer's cancelled status to false
            const { data, error } = await supabase
                .from('customer_profiles')
                .update({ cancelled: false })
                .eq('user_id', restoreMemberTarget.user_id)
                .select();

            if (error) {
                console.error('Database error:', error);
                throw error;
            }

            if (!data || data.length === 0) {
                throw new Error('Failed to restore member');
            }

            setRestoreMemberSuccess(t('member_restored_successfully'));

            // Remove the member from the current list
            setMembers(prevMembers =>
                prevMembers.filter(member => member.user_id !== restoreMemberTarget.user_id)
            );

            // Close modal after 1.5 seconds
            setTimeout(() => {
                setShowRestoreMemberModal(false);
                setRestoreMemberTarget(null);
            }, 1500);

        } catch (error) {
            console.error('Error restoring member:', error);
            setRestoreMemberError(error.message || t('member_restore_error'));
        } finally {
            setRestoreMemberLoading(false);
        }
    };

    const closeRestoreMemberModal = () => {
        setShowRestoreMemberModal(false);
        setRestoreMemberTarget(null);
        setRestoreMemberError('');
        setRestoreMemberSuccess('');
    };

    if (loading) {
        return <div>{t('loading_members')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('cancelled_members')}</h2>

            {/* Filter Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('search_username')}</Form.Label>
                                        <InputGroup>
                                            <Form.Control
                                                type="text"
                                                placeholder={t('please_enter_username')}
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                            />
                                        </InputGroup>
                                    </Form.Group>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('status_filter')}</Form.Label>
                                        <Form.Select
                                            value={statusFilter}
                                            onChange={(e) => setStatusFilter(e.target.value)}
                                        >
                                            <option value="">{t('please_select_status')}</option>
                                            <option value="pending">{t('pending_review')}</option>
                                            <option value="approved">{t('approved')}</option>
                                            <option value="rejected">{t('rejected')}</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('start_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('end_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Cancelled Members Table */}
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('username')}</th>
                                        <th>{t('real_name')}</th>
                                        <th>{t('id_number')}</th>
                                        <th>{t('id_front_image')}</th>
                                        <th>{t('id_back_image')}</th>
                                        <th>{t('status')}</th>
                                        <th>{t('registration_time')}</th>
                                        <th>{t('actions')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {filteredMembers.length === 0 ? (
                                        <tr>
                                            <td colSpan="8" className="text-center">{t('no_cancelled_members_found')}</td>
                                        </tr>
                                    ) : (
                                        filteredMembers.map(member => (
                                            <tr key={member.user_id}>
                                                <td>{member.users?.email || '-'}</td>
                                                <td>{member.real_name || '-'}</td>
                                                <td>{member.id_number || '-'}</td>
                                                <td>
                                                    {member.id_img_front ? (
                                                        <img
                                                            src={member.id_img_front}
                                                            alt="ID Front"
                                                            style={{
                                                                width: '60px',
                                                                height: '40px',
                                                                objectFit: 'cover',
                                                                borderRadius: '4px',
                                                                cursor: 'pointer'
                                                            }}
                                                            onClick={() => window.open(member.id_img_front, '_blank')}
                                                        />
                                                    ) : (
                                                        <span className="text-muted">-</span>
                                                    )}
                                                </td>
                                                <td>
                                                    {member.id_img_back ? (
                                                        <img
                                                            src={member.id_img_back}
                                                            alt="ID Back"
                                                            style={{
                                                                width: '60px',
                                                                height: '40px',
                                                                objectFit: 'cover',
                                                                borderRadius: '4px',
                                                                cursor: 'pointer'
                                                            }}
                                                            onClick={() => window.open(member.id_img_back, '_blank')}
                                                        />
                                                    ) : (
                                                        <span className="text-muted">-</span>
                                                    )}
                                                </td>
                                                <td><StatusBadge status={member.verify_status} type="review" /></td>
                                                <td>{member.users?.created_at ? new Date(member.users.created_at).toLocaleString() : '-'}</td>
                                                <td>
                                                    <div className="d-flex justify-content-center">
                                                        <Button
                                                            size="sm"
                                                            variant="outline-success"
                                                            onClick={() => handleRestoreMember(member)}
                                                            title={t('restore_member')}
                                                        >
                                                            <FaUserCheck />
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Restore Member Modal */}
            <Modal show={showRestoreMemberModal} onHide={closeRestoreMemberModal} size="md">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('restore_member')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeRestoreMemberModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {restoreMemberError && (
                        <Alert variant="danger" className="mb-3">
                            {restoreMemberError}
                        </Alert>
                    )}
                    {restoreMemberSuccess && (
                        <Alert variant="success" className="mb-3">
                            {restoreMemberSuccess}
                        </Alert>
                    )}

                    {restoreMemberTarget && (
                        <div className="mb-4">
                            <Card>
                                <Card.Header>
                                    <strong>{t('customer_info')}</strong>
                                </Card.Header>
                                <Card.Body>
                                    <p><strong>{t('username')}:</strong> {restoreMemberTarget.users?.email || '-'}</p>
                                    <p><strong>{t('real_name')}:</strong> {restoreMemberTarget.real_name || '-'}</p>
                                    <p><strong>{t('current_status')}:</strong> <StatusBadge status={restoreMemberTarget.verify_status} type="review" /></p>
                                </Card.Body>
                            </Card>
                        </div>
                    )}

                    <Alert variant="info" className="mb-3">
                        <strong>{t('info')}:</strong> {t('restore_member_info')}
                    </Alert>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeRestoreMemberModal} disabled={restoreMemberLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="success"
                        onClick={handleConfirmRestoreMember}
                        disabled={restoreMemberLoading}
                    >
                        {restoreMemberLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('processing')}
                            </>
                        ) : (
                            <>
                                <FaUserCheck className="me-1" />
                                {t('confirm_restore_member')}
                            </>
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default CancelledMembers;
