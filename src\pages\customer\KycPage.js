
import React, { useState, useEffect } from 'react';
import { Container, Form, Button, Card, Alert } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';

const KycPage = () => {
    const { t } = useTranslation();
    const [realName, setRealName] = useState('');
    const [idNumber, setIdNumber] = useState('');
    const [idImgFront, setIdImgFront] = useState(null);
    const [idImgBack, setIdImgBack] = useState(null);
    const [verifyStatus, setVerifyStatus] = useState(null); // null, 'pending', 'approved', 'rejected'
    const [loading, setLoading] = useState(true);
    const [submitting, setSubmitting] = useState(false);
    const [message, setMessage] = useState({ type: '', text: '' });

    useEffect(() => {
        const fetchKycStatus = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                setLoading(false);
                return;
            }

            const { data, error } = await supabase
                .from('customer_profiles')
                .select('real_name, id_number, id_img_front, id_img_back, verify_status')
                .eq('user_id', user.id)
                .single();

            if (error && error.code !== 'PGRST116') { // PGRST116 means no rows found
                console.error('Error fetching KYC status:', error);
                setMessage({ type: 'danger', text: t('failed_to_load_kyc_status') });
            } else if (data) {
                setRealName(data.real_name || '');
                setIdNumber(data.id_number || '');

                // Store the raw data from Supabase - getImageSrc will handle the conversion
                setIdImgFront(data.id_img_front || null);
                setIdImgBack(data.id_img_back || null);
                setVerifyStatus(data.verify_status || null);
            }
            setLoading(false);
        };

        fetchKycStatus();
    }, []);

    const handleFileChange = (e, setImage) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];

            // Validate file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                setMessage({ type: 'danger', text: t('invalid_file_type') });
                return;
            }

            // Validate file size (max 5MB)
            const maxSize = 5 * 1024 * 1024; // 5MB
            if (file.size > maxSize) {
                setMessage({ type: 'danger', text: t('file_too_large') });
                return;
            }

            setImage(file);
        }
    };

    // Helper function to convert file to base64
    const fileToBase64 = (file) => {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    };

    // Helper function to convert hex string to string
    const hexToString = (hex) => {
        let result = '';
        for (let i = 0; i < hex.length; i += 2) {
            result += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
        }
        return result;
    };

    // Helper function to get image source for display
    const getImageSrc = (imageData) => {
        if (!imageData) return null;

        // If it's a File object, create object URL
        if (imageData instanceof File) {
            return URL.createObjectURL(imageData);
        }

        // If it's already a data URL, return as is
        if (typeof imageData === 'string' && imageData.startsWith('data:')) {
            return imageData;
        }

        // Handle PostgreSQL bytea hex format (starts with \x)
        if (typeof imageData === 'string' && imageData.startsWith('\\x')) {
            try {
                // Remove the \x prefix and convert hex to string
                const hexData = imageData.substring(2);
                const decodedString = hexToString(hexData);

                // The decoded string should be a data URL
                if (decodedString.startsWith('data:')) {
                    return decodedString;
                }

                // If it's just base64 data, add the data URL prefix
                return `data:image/jpeg;base64,${decodedString}`;
            } catch (error) {
                console.error('Error converting hex data:', error);
                return null;
            }
        }

        // Handle Supabase bytea field - it might be returned as a Uint8Array or Buffer
        if (imageData instanceof Uint8Array || (imageData && imageData.type === 'Buffer')) {
            try {
                // Convert Uint8Array or Buffer to base64
                let base64String;
                if (imageData instanceof Uint8Array) {
                    // Convert Uint8Array to base64
                    base64String = btoa(String.fromCharCode.apply(null, imageData));
                } else if (imageData.type === 'Buffer' && imageData.data) {
                    // Handle Node.js Buffer format from Supabase
                    const uint8Array = new Uint8Array(imageData.data);
                    base64String = btoa(String.fromCharCode.apply(null, uint8Array));
                } else {
                    return null;
                }
                return `data:image/jpeg;base64,${base64String}`;
            } catch (error) {
                console.error('Error converting image data:', error);
                return null;
            }
        }

        // If it's a base64 string, convert to data URL
        if (typeof imageData === 'string') {
            return `data:image/jpeg;base64,${imageData}`;
        }

        return null;
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSubmitting(true);
        setMessage({ type: '', text: '' });

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Supabase not initialized');
            }

            // Get current user ID
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                throw new Error('User not authenticated');
            }

            // Prepare form data
            const formData = {
                real_name: realName,
                id_number: idNumber,
                user_id: user.id
            };

            // Convert files to base64 if they are new File objects
            if (idImgFront instanceof File) {
                try {
                    const base64Data = await fileToBase64(idImgFront);
                    formData.id_img_front_base64 = base64Data;
                } catch (error) {
                    throw new Error('Failed to process front image: ' + error.message);
                }
            }

            if (idImgBack instanceof File) {
                try {
                    const base64Data = await fileToBase64(idImgBack);
                    formData.id_img_back_base64 = base64Data;
                } catch (error) {
                    throw new Error('Failed to process back image: ' + error.message);
                }
            }

            const response = await fetch(`${window.wpData.apiUrl}submit-kyc`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': window.wpData.nonce
                },
                body: JSON.stringify(formData)
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(t(result.error_code) || 'Failed to submit KYC');
            }

            // Update local state - keep the File objects for preview, they will be converted to base64 data URLs for display
            setMessage({ type: 'success', text: t('kyc_submit_success') });
            setVerifyStatus('pending');

            // Show any processing warnings if present
            if (result.errors && result.errors.length > 0) {
                console.warn('Processing warnings:', result.errors);
            }

        } catch (error) {
            console.error('KYC submission error:', error);
            setMessage({ type: 'danger', text: t('failed_to_submit_kyc') + ': ' + error.message });
        }

        setSubmitting(false);
    };

    if (loading) {
        return <div>{t('loading_kyc_status')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('kyc_verification')}</h2>
            <Card>
                <Card.Body>
                    {message.text && <Alert variant={message.type}>{message.text}</Alert>}
                    
                    {verifyStatus === 'approved' && (
                        <Alert variant="success">{t('kyc_approved')}</Alert>
                    )}
                    {verifyStatus === 'pending' && (
                        <Alert variant="info">{t('kyc_pending_review')}</Alert>
                    )}
                    {verifyStatus === 'rejected' && (
                        <Alert variant="danger">{t('kyc_rejected')}</Alert>
                    )}
                    {verifyStatus === null && (
                        <Alert variant="warning">{t('kyc_not_submitted')}</Alert>
                    )}

                    <Form onSubmit={handleSubmit}>
                        <Form.Group className="mb-3">
                            <Form.Label><span className="text-danger">*</span>{t('real_name')}</Form.Label>
                            <Form.Control 
                                type="text" 
                                value={realName} 
                                onChange={(e) => setRealName(e.target.value)} 
                                required 
                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}
                            />
                        </Form.Group>
                        <Form.Group className="mb-3">
                            <Form.Label><span className="text-danger">*</span>{t('id_number')}</Form.Label>
                            <Form.Control 
                                type="text" 
                                value={idNumber} 
                                onChange={(e) => setIdNumber(e.target.value)} 
                                required 
                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}
                            />
                        </Form.Group>
                        <Form.Group className="mb-3">
                            <Form.Label>{t('id_front')}</Form.Label>
                            <Form.Control 
                                type="file" 
                                onChange={(e) => handleFileChange(e, setIdImgFront)} 
                                accept="image/*" 
                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}
                            />
                            {idImgFront && (
                                <img src={getImageSrc(idImgFront)} alt="ID Front" className="img-thumbnail mt-2" style={{ maxWidth: '200px' }} />
                            )}
                        </Form.Group>
                        <Form.Group className="mb-3">
                            <Form.Label>{t('id_back')}</Form.Label>
                            <Form.Control 
                                type="file" 
                                onChange={(e) => handleFileChange(e, setIdImgBack)} 
                                accept="image/*" 
                                disabled={verifyStatus === 'pending' || verifyStatus === 'approved'}
                            />
                            {idImgBack && (
                                <img src={getImageSrc(idImgBack)} alt="ID Back" className="img-thumbnail mt-2" style={{ maxWidth: '200px' }} />
                            )}
                        </Form.Group>
                        
                        <Button 
                            variant="primary" 
                            type="submit" 
                            disabled={submitting || verifyStatus === 'pending' || verifyStatus === 'approved'}
                        >
                            {submitting ? t('submitting') : t('submit_review')}
                        </Button>
                    </Form>
                </Card.Body>
            </Card>
        </Container>
    );
};

export default KycPage;
