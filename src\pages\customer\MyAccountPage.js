
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, ListGroup, Table, Button, Nav, Form, InputGroup, Alert } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { getSupabase } from '../../supabaseClient';
import { Link } from 'react-router-dom';
import { FaEye, FaEyeSlash } from "react-icons/fa";
import bcrypt from 'bcryptjs';
import { FaHeadset, FaEnvelopeOpenText, FaUserShield, FaUnlockAlt, FaKey } from "react-icons/fa";
import { DataGrid } from '@mui/x-data-grid';
import { Box, Chip } from '@mui/material';
import { zhCN, jaJP, enUS } from '@mui/x-data-grid/locales';

const MyAccountPage = () => {
    const { t, i18n } = useTranslation();
    const [assets, setAssets] = useState([]);
    const [withdrawals, setWithdrawals] = useState([]);
    const [loading, setLoading] = useState(true);
    const [withdrawalsLoading, setWithdrawalsLoading] = useState(true);
    const [activeTab, setActiveTab] = useState('overview'); // overview, deposit, withdraw, exchange
    const [withdrawConfirmed, setWithdrawConfirmed] = useState(false);

    // Withdraw form states
    const [withdrawAmount, setWithdrawAmount] = useState('');
    const [withdrawAddress, setWithdrawAddress] = useState('');
    const [withdrawPassword, setWithdrawPassword] = useState('');
    const [showWithdrawPassword, setShowWithdrawPassword] = useState(false);
    const [withdrawLoading, setWithdrawLoading] = useState(false);
    const [withdrawError, setWithdrawError] = useState('');
    const [withdrawSuccess, setWithdrawSuccess] = useState('');
    const [withdrawRemark, setWithdrawRemark] = useState('');

    // Get MUI DataGrid locale based on current language
    const getDataGridLocale = () => {
        switch (i18n.language) {
            case 'zh':
                return zhCN.components.MuiDataGrid.defaultProps.localeText;
            case 'ja':
                return jaJP.components.MuiDataGrid.defaultProps.localeText;
            default:
                return enUS.components.MuiDataGrid.defaultProps.localeText;
        }
    };

    useEffect(() => {
        const fetchAssets = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            const { data, error } = await supabase
                .from('user_assets')
                .select('*')
                .eq('user_id', user.id);

            if (error) {
                console.error('Error fetching assets:', error);
            } else {
                setAssets(data);
            }
            setLoading(false);
        };

        const fetchWithdrawals = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setWithdrawalsLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setWithdrawalsLoading(false);
                return; // User not logged in
            }

            const { data, error } = await supabase
                .from('withdrawals')
                .select(`
                    id,
                    request_amount,
                    final_amount,
                    fee,
                    status,
                    requested_at,
                    reviewed_at,
                    currency_code,
                    address
                `)
                .eq('user_id', user.id)
                .order('requested_at', { ascending: false });

            if (error) {
                console.error('Error fetching withdrawals:', error);
            } else {
                setWithdrawals(data || []);
            }
            setWithdrawalsLoading(false);
        };

        fetchAssets();
        fetchWithdrawals();
    }, []);

    // Get FIL balance for withdraw
    const getFilBalance = () => {
        const filAsset = assets.find(asset => asset.currency_code === 'FIL');
        return filAsset ? parseFloat(filAsset.balance_available) : 0;
    };

    // Define columns for withdrawals DataGrid
    const withdrawalsColumns = [
        {
            field: 'id',
            headerName: t('withdrawal_id'),
            width: 150,
            renderCell: (params) => params.value.substring(0, 8) + '...'
        },
        {
            field: 'request_amount',
            headerName: t('request_amount'),
            width: 150,
            type: 'number',
            renderCell: (params) => `${params.value} ${params.row.currency_code || 'FIL'}`
        },
        {
            field: 'final_amount',
            headerName: t('final_amount'),
            width: 150,
            type: 'number',
            renderCell: (params) => `${params.value} ${params.row.currency_code || 'FIL'}`
        },
        {
            field: 'fee',
            headerName: t('fee'),
            width: 120,
            type: 'number',
            renderCell: (params) => `${params.value} ${params.row.currency_code || 'FIL'}`
        },
        {
            field: 'status',
            headerName: t('status'),
            width: 120,
            renderCell: (params) => (
                <Chip
                    label={t(params.value)}
                    color={
                        params.value === 'approved' ? 'success' :
                        params.value === 'rejected' ? 'error' :
                        'warning'
                    }
                    size="small"
                />
            )
        },
        {
            field: 'requested_at',
            headerName: t('requested_at'),
            width: 180,
            renderCell: (params) => new Date(params.value).toLocaleString()
        }
    ];

    // Handle withdraw form submission
    const handleWithdrawSubmit = async (e) => {
        e.preventDefault();
        setWithdrawError('');
        setWithdrawSuccess('');

        // Validation
        const amount = parseFloat(withdrawAmount);
        const availableBalance = getFilBalance();

        if (!amount || amount < 1) {
            setWithdrawError(t('amount_too_small'));
            return;
        }

        if (amount > availableBalance -0.01) {
            setWithdrawError(t('amount_too_large'));
            return;
        }

        if (!withdrawAddress.trim()) {
            setWithdrawError(t('invalid_address'));
            return;
        }

        if (!withdrawPassword.trim()) {
            setWithdrawError(t('withdraw_password_required'));
            return;
        }

        if (!withdrawConfirmed) {
            setWithdrawError(t('please_confirm_warning'));
            return;
        }

        setWithdrawLoading(true);

        try {
            // Get Supabase session for authentication
            const supabase = getSupabase();
            const { data: { session } } = await supabase.auth.getSession();
            const { data: { user } } = await supabase.auth.getUser();

            const headers = {
                'Content-Type': 'application/json',
                'X-WP-Nonce': window.wpData.nonce
            };

            // Add Supabase auth token if available
            if (session?.access_token) {
                headers['Authorization'] = `Bearer ${session.access_token}`;
                console.log('Added auth token to request');
            } else {
                console.log('No auth token available');
            }

            // Call backend API to process withdrawal
            const response = await fetch(`${window.wpData.apiUrl}process-withdrawal`, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                    amount: amount,
                    address: withdrawAddress.trim(),
                    remark: withdrawRemark,
                    withdraw_password: withdrawPassword
                })
            });

            const result = await response.json();

            if (result.success) {
                setWithdrawSuccess(t('withdraw_request_sent'));
                // Clear form
                setWithdrawAmount('');
                setWithdrawAddress('');
                setWithdrawPassword('');
                setWithdrawConfirmed(false);
            } else {
                setWithdrawError(t(result.error_code) || result.message ||t('withdraw_request_failed'));
            }
        } catch (error) {
            console.error('Withdraw error:', error);
            setWithdrawError(t('withdraw_request_failed'));
        } finally {
            setWithdrawLoading(false);
        }
    };

    if (loading) {
        return <div>{t('loading_wallet')}</div>;
    }
    
    const renderContent = () => {
        switch (activeTab) {
            case 'overview':
                return (
                    <Table striped bordered hover responsive>
                        <thead>
                            <tr>
                                <th>{t('currency')}</th>
                                <th>{t('available_balance')}</th>
                                {/* <th>{t('locked_balance')}</th> */}
                                <th>{t('total_balance')}</th>
                                <th>{t('withdrawn')}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {assets.length === 0 ? (
                                <tr>
                                    <td colSpan="5" className="text-center">{t('no_assets')}</td>
                                </tr>
                            ) : (
                                assets.map(asset => (
                                    <tr key={asset.currency_code}>
                                        <td>{asset.currency_code}</td>
                                        <td>{asset.balance_available}</td>
                                        {/* <td>{asset.balance_locked}</td> */}
                                        <td>{asset.balance_total}</td>
                                        <td>{asset.withdrawn_total}</td>
                                    </tr>
                                ))
                            )}
                        </tbody>
                    </Table>
                );
            // case 'deposit':
            //     return <div><FaHeadset /> {t('deposit_coming_soon')}</div>; 
            case 'withdraw':
                return (
                    <div>
                        <Form onSubmit={handleWithdrawSubmit}>
                            {withdrawError && <Alert variant="danger">{withdrawError}</Alert>}
                            {withdrawSuccess && <Alert variant="success">{withdrawSuccess}</Alert>}

                            {/* Withdraw Amount */}
                            <Form.Group className="mb-3">
                                <Form.Label>
                                    <span className="text-danger">*</span> {t('withdraw_amount')}
                                </Form.Label>
                                <InputGroup>
                                    <Form.Control
                                        type="number"
                                        step="0.000001"
                                        min="1"
                                        placeholder={`${t('minimum_amount')}: 1`}
                                        value={withdrawAmount}
                                        onChange={(e) => setWithdrawAmount(e.target.value)}
                                        required
                                    />
                                    <InputGroup.Text>FIL</InputGroup.Text>
                                </InputGroup>
                                <Form.Text className="text-muted">
                                    {t('available_balance')} {getFilBalance().toFixed(10)} FIL<br />
                                    {t('minimum_1_fil')}
                                </Form.Text>
                            </Form.Group>

                            {/* Withdraw Address */}
                            <Form.Group className="mb-3">
                                <Form.Label>
                                    <span className="text-danger">*</span> {t('withdraw_address')}
                                </Form.Label>
                                <Form.Control
                                    type="text"
                                    placeholder={t('enter_address')}
                                    value={withdrawAddress}
                                    onChange={(e) => setWithdrawAddress(e.target.value)}
                                    required
                                />
                            </Form.Group>

                            {/* Withdraw Remark
                            <Form.Group className="mb-3">
                                <Form.Label>
                                    {t('withdraw_remark')}
                                </Form.Label>
                                <Form.Control
                                    type="text"
                                    placeholder={t('enter_remark')}
                                    value={withdrawRemark}
                                    onChange={(e) => setWithdrawRemark(e.target.value)}
                                />
                            </Form.Group> */}

                            {/* Withdraw Password */}
                            <Form.Group className="mb-3">
                                <Form.Label><span className="text-danger">*</span>{t('withdraw_password')}</Form.Label>
                                <InputGroup>
                                    <Form.Control
                                        type={showWithdrawPassword ? "text" : "password"}
                                        placeholder={t('enter_withdraw_password')}
                                        value={withdrawPassword}
                                        onChange={(e) => setWithdrawPassword(e.target.value)}
                                        required
                                    />
                                    <Button
                                        variant="outline-secondary"
                                        onClick={() => setShowWithdrawPassword(!showWithdrawPassword)}
                                        title={showWithdrawPassword ? t('hide_password') : t('show_password')}
                                    >
                                        {showWithdrawPassword ? <FaEyeSlash /> : <FaEye />}
                                    </Button>
                                </InputGroup>
                            </Form.Group>

                            <Form.Group className="mb-3">
                                <Form.Check
                                    type="checkbox"
                                    id="withdraw-confirmation"
                                    checked={withdrawConfirmed}
                                    onChange={(e) => setWithdrawConfirmed(e.target.checked)}
                                    label={
                                        <span className="text-muted">
                                            {t('withdraw_warning')}
                                        </span>
                                    }
                                    required
                                />
                            </Form.Group>
                            {/* Submit Button */}
                            <Button
                                type="submit"
                                variant="primary"
                                className="w-100"
                                disabled={withdrawLoading || !withdrawConfirmed}
                            >
                                {withdrawLoading ? (
                                    <>
                                        <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                        {t('processing')}
                                    </>
                                ) : (
                                    t('confirm')
                                )}
                            </Button>
                        </Form>
                    </div>
                );
            // case 'exchange':
            //     return <div>{t('exchange_coming_soon')}</div>; // Placeholder for exchange UI
            default:
                return null;
        }
    };

    return (
        <Container>
            <h2 className="mb-4">{t('my_wallet')}</h2>
                <Row>
                    <Col>
                        <Card>
                            <Card.Header>
                                <Nav variant="tabs" defaultActiveKey="overview" onSelect={(selectedKey) => setActiveTab(selectedKey)}>
                                    <Nav.Item>
                                        <Nav.Link eventKey="overview">{t('overview')}</Nav.Link>
                                    </Nav.Item>
                                    {/* <Nav.Item>
                                        <Nav.Link eventKey="deposit">{t('deposit')}</Nav.Link>
                                    </Nav.Item> */}
                                    <Nav.Item>
                                        <Nav.Link eventKey="withdraw">{t('withdraw')}</Nav.Link>
                                    </Nav.Item>
                                    {/* <Nav.Item>
                                        <Nav.Link eventKey="exchange">{t('exchange')}</Nav.Link>
                                    </Nav.Item> */}
                                </Nav>
                            </Card.Header>
                            <Card.Body>
                                {renderContent()}
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>

            <h2 className="mt-3 mb-4">{t('my_withdraw')}</h2>
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Box sx={{ height: 300, width: '100%' }}>
                                <DataGrid
                                    rows={withdrawals}
                                    columns={withdrawalsColumns}
                                    initialState={{
                                        pagination: {
                                            paginationModel: { page: 0, pageSize: 3 },
                                        },
                                    }}
                                    pageSizeOptions={[3, 10, 20]}
                                    disableRowSelectionOnClick
                                    loading={withdrawalsLoading}
                                    localeText={{
                                        ...getDataGridLocale(),
                                        noRowsLabel: t('no_withdrawals_record')
                                    }}
                                    sx={{
                                        '& .MuiDataGrid-cell': {
                                            borderBottom: '1px solid #e0e0e0',
                                        },
                                        '& .MuiDataGrid-columnHeaders': {
                                            backgroundColor: '#f5f5f5',
                                            borderBottom: '2px solid #e0e0e0',
                                        },
                                        '& .MuiDataGrid-row:hover': {
                                            backgroundColor: '#f9f9f9',
                                        }
                                    }}
                                />
                            </Box>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <h2 className="mt-3 mb-4">{t('my_account')}</h2>
            <Row>
                <Col md={6}>
                    <Card>
                        <Card.Header>{t('personal_info')}</Card.Header>
                        <ListGroup variant="flush">
                            <ListGroup.Item>
                                <FaUserShield className="me-2"/><Link to="/my/kyc">{t('kyc_verification')}</Link>
                            </ListGroup.Item>
                            <ListGroup.Item>
                                <FaUnlockAlt className="me-2"/><Link to="/my/change-login-pass">{t('change_login_password')}</Link>
                            </ListGroup.Item>
                            <ListGroup.Item>
                                <FaKey className="me-2"/><Link to="/my/change-withdraw-pass">{t('change_withdraw_password')}</Link>
                            </ListGroup.Item>
                        </ListGroup>
                    </Card>
                </Col>
                {/* <Col md={6}>
                    <Card>
                        <Card.Header>{t('my_recommendations')}</Card.Header>
                        <ListGroup variant="flush">
                            <ListGroup.Item>
                                <FaEnvelopeOpenText className="me-2"/><Link to="/my/recommend">{t('view_my_recommendations')}</Link>
                            </ListGroup.Item>
                        </ListGroup>
                    </Card>
                </Col> */}
            </Row>
        </Container>
    );
};

export default MyAccountPage;
