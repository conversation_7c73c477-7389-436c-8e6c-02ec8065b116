import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Form, InputGroup, Dropdown, Modal, Alert, Pagination } from 'react-bootstrap';
import { FaPlus, FaUserCheck, FaExchangeAlt, FaCheck, FaTimes, FaDownload, FaKey, FaUserTimes } from 'react-icons/fa';
import { getSupabase, getCurrentMakerId } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import StatusBadge from '../../components/StatusBadge';

const Members = () => {
    const { t } = useTranslation();
    const [members, setMembers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [kycMonthFilter, setKycMonthFilter] = useState('');
    const [filteredMembers, setFilteredMembers] = useState([]);

    // Helper function to convert hex string to string
    const hexToString = (hex) => {
        let result = '';
        for (let i = 0; i < hex.length; i += 2) {
            result += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
        }
        return result;
    };

    // Helper function to open image in new window
    const openImageInNewWindow = (imageData, title = 'Image') => {
        const imgSrc = getImageSrc(imageData);
        if (imgSrc) {
            const html = `
                <!DOCTYPE html>
                <html>
                    <head>
                        <title>${title}</title>
                        <style>
                            body {
                                margin: 0;
                                padding: 20px;
                                background: #f0f0f0;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                min-height: 100vh;
                                font-family: Arial, sans-serif;
                            }
                            img {
                                max-width: 100%;
                                max-height: 100%;
                                object-fit: contain;
                                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                                border-radius: 8px;
                            }
                        </style>
                    </head>
                    <body>
                        <img src="${imgSrc}" alt="${title}" />
                    </body>
                </html>
            `;

            const blob = new Blob([html], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const newWindow = window.open(url, '_blank');

            // Clean up the blob URL after the window loads
            if (newWindow) {
                newWindow.addEventListener('load', () => {
                    setTimeout(() => URL.revokeObjectURL(url), 1000);
                });
            }
        }
    };

    // Helper function to convert bytea/base64 data to data URL for display
    const getImageSrc = (imageData) => {
        if (!imageData) return null;

        // If it's already a data URL, return as is
        if (typeof imageData === 'string' && imageData.startsWith('data:')) {
            return imageData;
        }

        // Handle PostgreSQL bytea hex format (starts with \x)
        if (typeof imageData === 'string' && imageData.startsWith('\\x')) {
            try {
                // Remove the \x prefix and convert hex to string
                const hexData = imageData.substring(2);
                const decodedString = hexToString(hexData);

                // The decoded string should be a data URL
                if (decodedString.startsWith('data:')) {
                    return decodedString;
                }

                // If it's just base64 data, add the data URL prefix
                return `data:image/jpeg;base64,${decodedString}`;
            } catch (error) {
                console.error('Error converting hex data:', error);
                return null;
            }
        }

        // Handle Supabase bytea field - it might be returned as a Uint8Array or Buffer
        if (imageData instanceof Uint8Array || (imageData && imageData.type === 'Buffer')) {
            try {
                // Convert Uint8Array or Buffer to base64
                let base64String;
                if (imageData instanceof Uint8Array) {
                    // Convert Uint8Array to base64
                    base64String = btoa(String.fromCharCode.apply(null, imageData));
                } else if (imageData.type === 'Buffer' && imageData.data) {
                    // Handle Node.js Buffer format from Supabase
                    const uint8Array = new Uint8Array(imageData.data);
                    base64String = btoa(String.fromCharCode.apply(null, uint8Array));
                } else {
                    return null;
                }
                return `data:image/jpeg;base64,${base64String}`;
            } catch (error) {
                console.error('Error converting image data:', error);
                return null;
            }
        }

        // If it's a base64 string, convert to data URL
        if (typeof imageData === 'string') {
            return `data:image/jpeg;base64,${imageData}`;
        }

        return null;
    };

    // Pagination states
    const [currentPage, setCurrentPage] = useState(1);
    const [membersPerPage] = useState(10);
    const [paginatedMembers, setPaginatedMembers] = useState([]);

    const [showKycModal, setShowKycModal] = useState(false);
    const [selectedMember, setSelectedMember] = useState(null);
    const [kycLoading, setKycLoading] = useState(false);
    const [kycError, setKycError] = useState('');
    const [kycSuccess, setKycSuccess] = useState('');

    // Change Agent Modal states
    const [showChangeAgentModal, setShowChangeAgentModal] = useState(false);
    const [changeAgentMember, setChangeAgentMember] = useState(null);
    const [availableAgents, setAvailableAgents] = useState([]);
    const [selectedNewAgent, setSelectedNewAgent] = useState('');
    const [changeAgentLoading, setChangeAgentLoading] = useState(false);
    const [changeAgentError, setChangeAgentError] = useState('');
    const [changeAgentSuccess, setChangeAgentSuccess] = useState('');

    // Add Member Modal states
    const [showAddMemberModal, setShowAddMemberModal] = useState(false);
    const [newMemberEmail, setNewMemberEmail] = useState('');
    const [newMemberPassword, setNewMemberPassword] = useState('');
    const [newMemberInviteCode, setNewMemberInviteCode] = useState('');
    const [addMemberLoading, setAddMemberLoading] = useState(false);
    const [addMemberError, setAddMemberError] = useState('');
    const [addMemberSuccess, setAddMemberSuccess] = useState('');

    // Change Password Modal states
    const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);
    const [changePasswordMember, setChangePasswordMember] = useState(null);
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [changePasswordLoading, setChangePasswordLoading] = useState(false);
    const [changePasswordError, setChangePasswordError] = useState('');
    const [changePasswordSuccess, setChangePasswordSuccess] = useState('');

    // Cancel Member Modal states
    const [showCancelMemberModal, setShowCancelMemberModal] = useState(false);
    const [cancelMemberTarget, setCancelMemberTarget] = useState(null);
    const [cancelMemberLoading, setCancelMemberLoading] = useState(false);
    const [cancelMemberError, setCancelMemberError] = useState('');
    const [cancelMemberSuccess, setCancelMemberSuccess] = useState('');

    useEffect(() => {
        const fetchMembers = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return;
            }

            // 使用JOIN查询优化，避免URL过长的问题
            const { data: enrichedMembers, error: profileError } = await supabase
                .from('customer_profiles')
                .select(`
                    user_id,
                    real_name,
                    id_number,
                    id_img_front,
                    id_img_back,
                    verify_status,
                    cancelled,
                    kyc_submitted_at,
                    users!inner(
                        id,
                        email,
                        created_at
                    )
                `)
                .eq('cancelled', false)
                .order('created_at', { ascending: false });

            if (profileError || !enrichedMembers) {
                console.error('Error fetching customer_profiles with users:', profileError);
                setLoading(false);
                return;
            }

            setMembers(enrichedMembers);
            setLoading(false);
        };

        fetchMembers();
    }, []);

    // Filter members based on search criteria
    useEffect(() => {
        let filtered = members;

        // Search by username (email)
        if (searchTerm) {
            filtered = filtered.filter(member =>
                member.users?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                member.real_name?.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // Filter by status
        if (statusFilter) {
            filtered = filtered.filter(member => member.verify_status === statusFilter);
        }

        // Filter by KYC submission month
        if (kycMonthFilter) {
            filtered = filtered.filter(member => {
                if (!member.kyc_submitted_at) return false;
                const kycDate = new Date(member.kyc_submitted_at);
                const filterYear = parseInt(kycMonthFilter.split('-')[0]);
                const filterMonth = parseInt(kycMonthFilter.split('-')[1]);
                return kycDate.getFullYear() === filterYear && (kycDate.getMonth() + 1) === filterMonth;
            });
        }

        // Filter by date range
        if (startDate) {
            filtered = filtered.filter(member =>
                new Date(member.users?.created_at) >= new Date(startDate)
            );
        }
        if (endDate) {
            filtered = filtered.filter(member =>
                new Date(member.users?.created_at) <= new Date(endDate)
            );
        }

        setFilteredMembers(filtered);
        setCurrentPage(1); // Reset to first page when filters change
    }, [members, searchTerm, statusFilter, kycMonthFilter, startDate, endDate]);

    // Paginate filtered members
    useEffect(() => {
        const indexOfLastMember = currentPage * membersPerPage;
        const indexOfFirstMember = indexOfLastMember - membersPerPage;
        const currentMembers = filteredMembers.slice(indexOfFirstMember, indexOfLastMember);
        setPaginatedMembers(currentMembers);
    }, [filteredMembers, currentPage, membersPerPage]);

    // Pagination handlers
    const handlePageChange = (pageNumber) => {
        setCurrentPage(pageNumber);
    };

    const totalPages = Math.ceil(filteredMembers.length / membersPerPage);

    // Export filtered members to CSV
    const exportToCSV = () => {
        if (filteredMembers.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('username'),
            t('real_name'),
            t('id_number'),
            t('id_front_image'),
            t('id_back_image'),
            t('status'),
            t('kyc_submitted_at'),
            t('registration_time')
        ];

        // Convert data to CSV format
        const csvData = filteredMembers.map(member => [
            member.users?.email || '-',
            member.real_name || '-',
            member.id_number || '-',
            member.id_img_front ? 'Yes' : 'No',
            member.id_img_back ? 'Yes' : 'No',
            t(member.verify_status) || 'pending',
            member.kyc_submitted_at ? new Date(member.kyc_submitted_at).toLocaleString() : '-',
            member.users?.created_at ? new Date(member.users.created_at).toLocaleString() : '-'
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `maker_members_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    // Fetch available agents for change agent modal
    const fetchAvailableAgents = async () => {
        const supabase = getSupabase();
        if (!supabase) return;

        try {
            // Get current user (should be an agent)
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            // Query agent_profiles and join with users to get email
            const { data: agents, error } = await supabase
                .from('agent_profiles')
                .select(`
                    user_id,
                    brand_name,
                    commission_pct,
                    users(email)
                `);

            if (error) {
                console.error('Error fetching agents:', error);
                return;
            }
            setAvailableAgents(agents || []);
        } catch (error) {
            console.error('Error in fetchAvailableAgents:', error);
        }
    };

    const handleAddMember = () => {
        setShowAddMemberModal(true);
        setNewMemberEmail('');
        setNewMemberPassword('');
        setNewMemberInviteCode('');
        setAddMemberError('');
        setAddMemberSuccess('');
    };

    const handleConfirmAddMember = async () => {
        if (!newMemberEmail || !newMemberPassword || !newMemberInviteCode) {
            setAddMemberError(t('all_fields_required'));
            return;
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(newMemberEmail)) {
            setAddMemberError(t('invalid_email_format'));
            return;
        }

        // Validate password length
        if (newMemberPassword.length < 6) {
            setAddMemberError(t('password_min_length'));
            return;
        }

        setAddMemberLoading(true);
        setAddMemberError('');
        setAddMemberSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Get current user (maker)
            const { data: { user: currentUser } } = await supabase.auth.getUser();
            if (!currentUser) {
                throw new Error('Maker not authenticated');
            }

            // Step 1: Create auth user using admin API
            // Note: This requires service key, so we need to call a backend endpoint
            const createUserResponse = await fetch(`${window.wpData.apiUrl}create-member`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': window.wpData.nonce
                },
                body: JSON.stringify({
                    email: newMemberEmail,
                    password: newMemberPassword,
                    invite_code: newMemberInviteCode
                })
            });

            if (!createUserResponse.ok) {
                const errorData = await createUserResponse.json();
                throw new Error(errorData.message || 'Failed to create member');
            }

            const result = await createUserResponse.json();

            if (!result.success) {
                throw new Error(t(result.error_code) || result.message || 'Failed to create member');
            }

            setAddMemberSuccess(t('member_created_successfully'));

            // Close modal and refresh after 2 seconds to show success message
            setTimeout(() => {
                setShowAddMemberModal(false);
                setNewMemberEmail('');
                setNewMemberPassword('');
                setNewMemberInviteCode('');
                // Refresh the members list
                window.location.reload();
            }, 2000);

        } catch (error) {
            console.error('Error creating member:', error);
            setAddMemberError(error.message || t('member_creation_error'));
        } finally {
            setAddMemberLoading(false);
        }
    };

    const closeAddMemberModal = () => {
        setShowAddMemberModal(false);
        setNewMemberEmail('');
        setNewMemberPassword('');
        setNewMemberInviteCode('');
        setAddMemberError('');
        setAddMemberSuccess('');
    };

    const handleKycReview = (member) => {
        setSelectedMember(member);
        setShowKycModal(true);
        setKycError('');
        setKycSuccess('');
    };

    const handleKycDecision = async (decision) => {
        if (!selectedMember) return;

        setKycLoading(true);
        setKycError('');
        setKycSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Get current user for audit log
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                throw new Error('User not authenticated');
            }

            // Get the old verify_status before updating
            const { data: oldData, error: selectError } = await supabase
                .from('customer_profiles')
                .select('verify_status')
                .eq('user_id', selectedMember.user_id)
                .single();

            if (selectError) {
                console.error('Error fetching old status:', selectError);
                throw selectError;
            }

            const oldStatus = oldData?.verify_status || 'not_submitted';

            // Update the verify_status
            const { data, error } = await supabase
                .from('customer_profiles')
                .update({ verify_status: decision })
                .eq('user_id', selectedMember.user_id)
                .select();

            if (error) {
                console.error('Database error:', error);
                throw error;
            }

            // Check if any rows were updated
            if (!data || data.length === 0) {
                throw new Error('Customer profile not found or no changes made');
            }

            // Insert audit log record
            const auditLogData = {
                user_id: user.id, // Current user (maker) who made the decision
                action: decision,
                object_table: 'customer_profiles',
                object_id: selectedMember.user_id, // The customer whose KYC was reviewed
                diff: {
                    old: { verify_status: oldStatus },
                    new: { verify_status: decision }
                }
            };

            const { error: auditError } = await supabase
                .from('audit_logs')
                .insert(auditLogData);

            if (auditError) {
                console.error('Error inserting audit log:', auditError);
                // Don't throw error here as the main operation succeeded
                // Just log the error for debugging
            }

            // Update local state
            setMembers(prevMembers =>
                prevMembers.map(member =>
                    member.user_id === selectedMember.user_id
                        ? { ...member, verify_status: decision }
                        : member
                )
            );

            // Also update the selected member
            setSelectedMember(prev => ({ ...prev, verify_status: decision }));

            setKycSuccess(decision === 'approved' ? t('kyc_approved_success') : t('kyc_rejected_success'));

            // Close modal after 1.5 seconds
            setTimeout(() => {
                setShowKycModal(false);
                setSelectedMember(null);
            }, 1500);

        } catch (error) {
            console.error('Error updating KYC status:', error);
            setKycError(error.message || t('kyc_update_error'));
        } finally {
            setKycLoading(false);
        }
    };

    const closeKycModal = () => {
        setShowKycModal(false);
        setSelectedMember(null);
        setKycError('');
        setKycSuccess('');
    };

    const handleChangeAgent = async (member) => {
        setChangeAgentMember(member);
        setChangeAgentError('');
        setChangeAgentSuccess('');
        setSelectedNewAgent('');

        // Fetch available agents
        await fetchAvailableAgents();

        setShowChangeAgentModal(true);
    };

    const handleConfirmChangeAgent = async () => {
        if (!changeAgentMember || !selectedNewAgent) {
            setChangeAgentError(t('please_select_agent'));
            return;
        }

        setChangeAgentLoading(true);
        setChangeAgentError('');
        setChangeAgentSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Update customer's agent_id
            const { data, error } = await supabase
                .from('customer_profiles')
                .update({ agent_id: selectedNewAgent })
                .eq('user_id', changeAgentMember.user_id)
                .select();

            if (error) {
                console.error('Database error:', error);
                throw error;
            }

            if (!data || data.length === 0) {
                throw new Error('Failed to update agent assignment');
            }

            setChangeAgentSuccess(t('agent_changed_successfully'));

            // Close modal after 1.5 seconds
            setTimeout(() => {
                setShowChangeAgentModal(false);
                setChangeAgentMember(null);
                setSelectedNewAgent('');
            }, 1500);

        } catch (error) {
            console.error('Error changing agent:', error);
            setChangeAgentError(error.message || t('agent_change_error'));
        } finally {
            setChangeAgentLoading(false);
        }
    };

    const closeChangeAgentModal = () => {
        setShowChangeAgentModal(false);
        setChangeAgentMember(null);
        setSelectedNewAgent('');
        setChangeAgentError('');
        setChangeAgentSuccess('');
    };

    const handleChangePassword = (member) => {
        setChangePasswordMember(member);
        setNewPassword('');
        setConfirmPassword('');
        setChangePasswordError('');
        setChangePasswordSuccess('');
        setShowChangePasswordModal(true);
    };

    const handleConfirmChangePassword = async () => {
        if (!newPassword || !confirmPassword) {
            setChangePasswordError(t('all_fields_required'));
            return;
        }

        if (newPassword.length < 6) {
            setChangePasswordError(t('password_min_length'));
            return;
        }

        if (newPassword !== confirmPassword) {
            setChangePasswordError(t('passwords_do_not_match'));
            return;
        }

        setChangePasswordLoading(true);
        setChangePasswordError('');
        setChangePasswordSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Call backend API to change password
            const changePasswordResponse = await fetch(`${window.wpData.apiUrl}change-member-password`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': window.wpData.nonce
                },
                body: JSON.stringify({
                    user_id: changePasswordMember.user_id,
                    new_password: newPassword
                })
            });

            if (!changePasswordResponse.ok) {
                const errorData = await changePasswordResponse.json();
                throw new Error(errorData.message || 'Failed to change password');
            }

            const result = await changePasswordResponse.json();

            if (!result.success) {
                throw new Error(t(result.error_code) || result.message || 'Failed to change password');
            }

            setChangePasswordSuccess(t('password_changed_successfully'));

            // Close modal after 2 seconds
            setTimeout(() => {
                setShowChangePasswordModal(false);
                setChangePasswordMember(null);
                setNewPassword('');
                setConfirmPassword('');
            }, 2000);

        } catch (error) {
            console.error('Error changing password:', error);
            setChangePasswordError(error.message || t('password_change_error'));
        } finally {
            setChangePasswordLoading(false);
        }
    };

    const closeChangePasswordModal = () => {
        setShowChangePasswordModal(false);
        setChangePasswordMember(null);
        setNewPassword('');
        setConfirmPassword('');
        setChangePasswordError('');
        setChangePasswordSuccess('');
    };

    const handleCancelMember = (member) => {
        setCancelMemberTarget(member);
        setCancelMemberError('');
        setCancelMemberSuccess('');
        setShowCancelMemberModal(true);
    };

    const handleConfirmCancelMember = async () => {
        if (!cancelMemberTarget) return;

        setCancelMemberLoading(true);
        setCancelMemberError('');
        setCancelMemberSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Update customer's cancelled status to true
            const { data, error } = await supabase
                .from('customer_profiles')
                .update({ cancelled: true })
                .eq('user_id', cancelMemberTarget.user_id)
                .select();

            if (error) {
                console.error('Database error:', error);
                throw error;
            }

            if (!data || data.length === 0) {
                throw new Error('Failed to cancel member');
            }

            setCancelMemberSuccess(t('member_cancelled_successfully'));

            // Remove the member from the current list
            setMembers(prevMembers =>
                prevMembers.filter(member => member.user_id !== cancelMemberTarget.user_id)
            );

            // Close modal after 1.5 seconds
            setTimeout(() => {
                setShowCancelMemberModal(false);
                setCancelMemberTarget(null);
            }, 1500);

        } catch (error) {
            console.error('Error cancelling member:', error);
            setCancelMemberError(error.message || t('member_cancel_error'));
        } finally {
            setCancelMemberLoading(false);
        }
    };

    const closeCancelMemberModal = () => {
        setShowCancelMemberModal(false);
        setCancelMemberTarget(null);
        setCancelMemberError('');
        setCancelMemberSuccess('');
    };

    if (loading) {
        return <div>{t('loading_members')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('member_list')}</h2>

            {/* Top Operation Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-end">
                                <Col md={3}>
                                    <Form.Group>
                                        <Form.Label>{t('export_data')}</Form.Label>
                                        <div>
                                            <Button
                                                variant="success"
                                                onClick={exportToCSV}
                                                className="mb-2 me-2"
                                                disabled={filteredMembers.length === 0}
                                            >
                                                <FaDownload className="me-1" />
                                                {t('export_all')}
                                            </Button>
                                            <Button
                                                variant="primary"
                                                onClick={handleAddMember}
                                                className="mb-2"
                                            >
                                                <FaPlus className="me-1" />
                                                {t('add_member')}
                                            </Button>
                                        </div>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('search_username')}</Form.Label>
                                        <InputGroup>
                                            <Form.Control
                                                type="text"
                                                placeholder={t('please_enter_username')}
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                            />
                                        </InputGroup>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('status_filter')}</Form.Label>
                                        <Form.Select
                                            value={statusFilter}
                                            onChange={(e) => setStatusFilter(e.target.value)}
                                        >
                                            <option value="">{t('please_select_status')}</option>
                                            <option value="pending">{t('pending_review')}</option>
                                            <option value="approved">{t('approved')}</option>
                                            <option value="rejected">{t('rejected')}</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={2}>
                                    <Form.Group>
                                        <Form.Label>{t('kyc_month_filter')}</Form.Label>
                                        <Form.Select
                                            value={kycMonthFilter}
                                            onChange={(e) => setKycMonthFilter(e.target.value)}
                                        >
                                            <option value="">{t('all_months')}</option>
                                            {(() => {
                                                const months = [];
                                                const currentDate = new Date();
                                                for (let i = 0; i < 12; i++) {
                                                    const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
                                                    const yearMonth = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                                                    const displayText = `${date.getFullYear()}年${date.getMonth() + 1}月`;
                                                    months.push(
                                                        <option key={yearMonth} value={yearMonth}>
                                                            {displayText}
                                                        </option>
                                                    );
                                                }
                                                return months;
                                            })()}
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col md={1}>
                                    <Form.Group>
                                        <Form.Label>{t('start_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={1}>
                                    <Form.Group>
                                        <Form.Label>{t('end_date')}</Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                        />
                                    </Form.Group>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Members Table */}
            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('username')}</th>
                                        <th>{t('real_name')}</th>
                                        <th>{t('id_number')}</th>
                                        <th>{t('id_front_image')}</th>
                                        <th>{t('id_back_image')}</th>
                                        <th>{t('status')}</th>
                                        <th>{t('kyc_submitted_at')}</th>
                                        <th>{t('registration_time')}</th>
                                        <th>{t('actions')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {paginatedMembers.length === 0 ? (
                                        <tr>
                                            <td colSpan="9" className="text-center">{t('no_members_found')}</td>
                                        </tr>
                                    ) : (
                                        paginatedMembers.map(member => (
                                            <tr key={member.user_id}>
                                                <td>{member.users?.email || '-'}</td>
                                                <td>{member.real_name || '-'}</td>
                                                <td>{member.id_number || '-'}</td>
                                                <td>
                                                    {member.id_img_front ? (
                                                        <img
                                                            src={getImageSrc(member.id_img_front)}
                                                            alt="ID Front"
                                                            style={{
                                                                width: '60px',
                                                                height: '40px',
                                                                objectFit: 'cover',
                                                                borderRadius: '4px',
                                                                cursor: 'pointer'
                                                            }}
                                                            onClick={() => openImageInNewWindow(member.id_img_front, 'ID Front Image')}
                                                        />
                                                    ) : (
                                                        <span className="text-muted">-</span>
                                                    )}
                                                </td>
                                                <td>
                                                    {member.id_img_back ? (
                                                        <img
                                                            src={getImageSrc(member.id_img_back)}
                                                            alt="ID Back"
                                                            style={{
                                                                width: '60px',
                                                                height: '40px',
                                                                objectFit: 'cover',
                                                                borderRadius: '4px',
                                                                cursor: 'pointer'
                                                            }}
                                                            onClick={() => openImageInNewWindow(member.id_img_back, 'ID Back Image')}
                                                        />
                                                    ) : (
                                                        <span className="text-muted">-</span>
                                                    )}
                                                </td>
                                                <td><StatusBadge status={member.verify_status} type="review" /></td>
                                                <td>{member.kyc_submitted_at ? new Date(member.kyc_submitted_at).toLocaleString() : '-'}</td>
                                                <td>{member.users?.created_at ? new Date(member.users.created_at).toLocaleString() : '-'}</td>
                                                <td>
                                                    <div className="d-flex justify-content-evenly">
                                                        <Button
                                                            size="sm"
                                                            variant="outline-primary"
                                                            onClick={() => handleKycReview(member)}
                                                            title={t('kyc_review')}
                                                        >
                                                            <FaUserCheck />
                                                        </Button>
                                                        <Button
                                                            size="sm"
                                                            variant="outline-warning"
                                                            onClick={() => handleChangeAgent(member)}
                                                            title={t('change_agent')}
                                                        >
                                                            <FaExchangeAlt />
                                                        </Button>
                                                        <Button
                                                            size="sm"
                                                            variant="outline-info"
                                                            onClick={() => handleChangePassword(member)}
                                                            title={t('change_password')}
                                                        >
                                                            <FaKey />
                                                        </Button>
                                                        <Button
                                                            size="sm"
                                                            variant="outline-danger"
                                                            onClick={() => handleCancelMember(member)}
                                                            title={t('cancel_member')}
                                                        >
                                                            <FaUserTimes />
                                                        </Button>
                                                    </div>
                                                </td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>

                            {/* Pagination */}
                            {totalPages > 1 && (
                                <div className="d-flex justify-content-center">
                                    <Pagination>
                                        <Pagination.First
                                            onClick={() => handlePageChange(1)}
                                            disabled={currentPage === 1}
                                        />
                                        <Pagination.Prev
                                            onClick={() => handlePageChange(currentPage - 1)}
                                            disabled={currentPage === 1}
                                        />

                                        {/* Show page numbers */}
                                        {[...Array(totalPages)].map((_, index) => {
                                            const pageNumber = index + 1;
                                            // Show first page, last page, current page, and pages around current page
                                            if (
                                                pageNumber === 1 ||
                                                pageNumber === totalPages ||
                                                (pageNumber >= currentPage - 2 && pageNumber <= currentPage + 2)
                                            ) {
                                                return (
                                                    <Pagination.Item
                                                        key={pageNumber}
                                                        active={pageNumber === currentPage}
                                                        onClick={() => handlePageChange(pageNumber)}
                                                    >
                                                        {pageNumber}
                                                    </Pagination.Item>
                                                );
                                            } else if (
                                                pageNumber === currentPage - 3 ||
                                                pageNumber === currentPage + 3
                                            ) {
                                                return <Pagination.Ellipsis key={pageNumber} />;
                                            }
                                            return null;
                                        })}

                                        <Pagination.Next
                                            onClick={() => handlePageChange(currentPage + 1)}
                                            disabled={currentPage === totalPages}
                                        />
                                        <Pagination.Last
                                            onClick={() => handlePageChange(totalPages)}
                                            disabled={currentPage === totalPages}
                                        />
                                    </Pagination>
                                </div>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* KYC Review Modal */}
            <Modal show={showKycModal} onHide={closeKycModal} size="lg">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('kyc_review')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeKycModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {selectedMember && (
                        <>
                            {kycError && (
                                <Alert variant="danger" className="mb-3">
                                    {kycError}
                                </Alert>
                            )}
                            {kycSuccess && (
                                <Alert variant="success" className="mb-3">
                                    {kycSuccess}
                                </Alert>
                            )}

                            <Row>
                                <Col md={6}>
                                    <Card className="mb-3">
                                        <Card.Header>
                                            <strong>{t('customer_info')}</strong>
                                        </Card.Header>
                                        <Card.Body>
                                            <p><strong>{t('username')}:</strong> {selectedMember.users?.email || '-'}</p>
                                            <p><strong>{t('real_name')}:</strong> {selectedMember.real_name || '-'}</p>
                                            <p><strong>{t('id_number')}:</strong> {selectedMember.id_number || '-'}</p>
                                            <p><strong>{t('current_status')}:</strong> <StatusBadge status={selectedMember.verify_status} type="review" /></p>
                                            <p><strong>{t('kyc_submitted_at')}:</strong> {selectedMember.kyc_submitted_at ? new Date(selectedMember.kyc_submitted_at).toLocaleString() : '-'}</p>
                                            <p><strong>{t('registration_time')}:</strong> {selectedMember.users?.created_at ? new Date(selectedMember.users.created_at).toLocaleString() : '-'}</p>
                                        </Card.Body>
                                    </Card>
                                </Col>
                                <Col md={6}>
                                    <Card className="mb-3">
                                        <Card.Header>
                                            <strong>{t('id_documents')}</strong>
                                        </Card.Header>
                                        <Card.Body>
                                            <div className="mb-3">
                                                <strong>{t('id_front_image')}:</strong>
                                                <div className="mt-2">
                                                    {selectedMember.id_img_front ? (
                                                        <img
                                                            src={getImageSrc(selectedMember.id_img_front)}
                                                            alt="ID Front"
                                                            style={{
                                                                width: '100%',
                                                                maxHeight: '150px',
                                                                objectFit: 'contain',
                                                                borderRadius: '4px',
                                                                cursor: 'pointer',
                                                                border: '1px solid #dee2e6'
                                                            }}
                                                            onClick={() => openImageInNewWindow(selectedMember.id_img_front, 'ID Front Image')}
                                                        />
                                                    ) : (
                                                        <div className="text-muted text-center py-3" style={{ border: '1px dashed #dee2e6', borderRadius: '4px' }}>
                                                            {t('no_image_uploaded')}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="mb-3">
                                                <strong>{t('id_back_image')}:</strong>
                                                <div className="mt-2">
                                                    {selectedMember.id_img_back ? (
                                                        <img
                                                            src={getImageSrc(selectedMember.id_img_back)}
                                                            alt="ID Back"
                                                            style={{
                                                                width: '100%',
                                                                maxHeight: '150px',
                                                                objectFit: 'contain',
                                                                borderRadius: '4px',
                                                                cursor: 'pointer',
                                                                border: '1px solid #dee2e6'
                                                            }}
                                                            onClick={() => openImageInNewWindow(selectedMember.id_img_back, 'ID Back Image')}
                                                        />
                                                    ) : (
                                                        <div className="text-muted text-center py-3" style={{ border: '1px dashed #dee2e6', borderRadius: '4px' }}>
                                                            {t('no_image_uploaded')}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </Card.Body>
                                    </Card>
                                </Col>
                            </Row>
                        </>
                    )}
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeKycModal} disabled={kycLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="danger"
                        onClick={() => handleKycDecision('rejected')}
                        disabled={kycLoading || selectedMember?.verify_status === 'rejected'}
                    >
                        {kycLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('processing')}
                            </>
                        ) : (
                            <>
                                <FaTimes className="me-1" />
                                {t('reject')}
                            </>
                        )}
                    </Button>
                    <Button
                        variant="success"
                        onClick={() => handleKycDecision('approved')}
                        disabled={kycLoading || selectedMember?.verify_status === 'approved'}
                    >
                        {kycLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('processing')}
                            </>
                        ) : (
                            <>
                                <FaCheck className="me-1" />
                                {t('approve')}
                            </>
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Change Agent Modal */}
            <Modal show={showChangeAgentModal} onHide={closeChangeAgentModal} size="md">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('change_agent')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeChangeAgentModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {changeAgentError && (
                        <Alert variant="danger" className="mb-3">
                            {changeAgentError}
                        </Alert>
                    )}
                    {changeAgentSuccess && (
                        <Alert variant="success" className="mb-3">
                            {changeAgentSuccess}
                        </Alert>
                    )}

                    {changeAgentMember && (
                        <div className="mb-4">
                            <Card>
                                <Card.Header>
                                    <strong>{t('customer_info')}</strong>
                                </Card.Header>
                                <Card.Body>
                                    <p><strong>{t('username')}:</strong> {changeAgentMember.users?.email || '-'}</p>
                                    <p><strong>{t('real_name')}:</strong> {changeAgentMember.real_name || '-'}</p>
                                    <p><strong>{t('current_status')}:</strong> <StatusBadge status={changeAgentMember.verify_status} type="review" /></p>
                                </Card.Body>
                            </Card>
                        </div>
                    )}

                    <Form.Group className="mb-3">
                        <Form.Label><strong>{t('select_new_agent')}</strong></Form.Label>
                        <Form.Select
                            value={selectedNewAgent}
                            onChange={(e) => setSelectedNewAgent(e.target.value)}
                            disabled={changeAgentLoading}
                        >
                            <option value="">{t('please_select_agent')}</option>
                            {availableAgents.map(agent => (
                                <option key={agent.user_id} value={agent.user_id}>
                                    {agent.brand_name || agent.users?.email || agent.user_id}
                                    {agent.commission_pct && ` (${agent.commission_pct}%)`}
                                </option>
                            ))}
                        </Form.Select>
                        {availableAgents.length === 0 && (
                            <Form.Text className="text-muted">
                                {t('no_available_agents')}
                            </Form.Text>
                        )}
                    </Form.Group>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeChangeAgentModal} disabled={changeAgentLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleConfirmChangeAgent}
                        disabled={changeAgentLoading || !selectedNewAgent || availableAgents.length === 0}
                    >
                        {changeAgentLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('processing')}
                            </>
                        ) : (
                            <>
                                <FaExchangeAlt className="me-1" />
                                {t('confirm_change')}
                            </>
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Add Member Modal */}
            <Modal show={showAddMemberModal} onHide={closeAddMemberModal} size="md">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('add_member')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeAddMemberModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {addMemberError && (
                        <Alert variant="danger" className="mb-3">
                            {addMemberError}
                        </Alert>
                    )}
                    {addMemberSuccess && (
                        <Alert variant="success" className="mb-3">
                            {addMemberSuccess}
                        </Alert>
                    )}

                    <Form>
                        <Form.Group className="mb-3">
                            <Form.Label><strong>{t('email_address')}</strong></Form.Label>
                            <Form.Control
                                type="email"
                                value={newMemberEmail}
                                onChange={(e) => setNewMemberEmail(e.target.value)}
                                placeholder={t('enter_email_address')}
                                disabled={addMemberLoading}
                                required
                            />
                            <Form.Text className="text-muted">
                                {t('member_email_help')}
                            </Form.Text>
                        </Form.Group>

                        <Form.Group className="mb-3">
                            <Form.Label><strong>{t('password')}</strong></Form.Label>
                            <Form.Control
                                type="password"
                                value={newMemberPassword}
                                onChange={(e) => setNewMemberPassword(e.target.value)}
                                placeholder={t('enter_password')}
                                disabled={addMemberLoading}
                                minLength={6}
                                required
                            />
                            <Form.Text className="text-muted">
                                {t('password_min_6_chars')}
                            </Form.Text>
                        </Form.Group>

                        <Form.Group className="mb-3">
                            <Form.Label><strong>{t('invite_code')}</strong></Form.Label>
                            <Form.Control
                                type="text"
                                value={newMemberInviteCode}
                                onChange={(e) => setNewMemberInviteCode(e.target.value)}
                                placeholder={t('enter_invite_code')}
                                disabled={addMemberLoading}
                                required
                            />
                            <Form.Text className="text-muted">
                                {t('invite_code_help')}
                            </Form.Text>
                        </Form.Group>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeAddMemberModal} disabled={addMemberLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleConfirmAddMember}
                        disabled={addMemberLoading || !newMemberEmail || !newMemberPassword || !newMemberInviteCode}
                    >
                        {addMemberLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('creating')}
                            </>
                        ) : (
                            <>
                                <FaPlus className="me-1" />
                                {t('create_member')}
                            </>
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Change Password Modal */}
            <Modal show={showChangePasswordModal} onHide={closeChangePasswordModal} size="md">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('change_password')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeChangePasswordModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {changePasswordError && (
                        <Alert variant="danger" className="mb-3">
                            {changePasswordError}
                        </Alert>
                    )}
                    {changePasswordSuccess && (
                        <Alert variant="success" className="mb-3">
                            {changePasswordSuccess}
                        </Alert>
                    )}

                    {changePasswordMember && (
                        <div className="mb-4">
                            <Card>
                                <Card.Header>
                                    <strong>{t('customer_info')}</strong>
                                </Card.Header>
                                <Card.Body>
                                    <p><strong>{t('username')}:</strong> {changePasswordMember.users?.email || '-'}</p>
                                    <p><strong>{t('real_name')}:</strong> {changePasswordMember.real_name || '-'}</p>
                                    <p><strong>{t('current_status')}:</strong> <StatusBadge status={changePasswordMember.verify_status} type="review" /></p>
                                </Card.Body>
                            </Card>
                        </div>
                    )}

                    <Form>
                        <Form.Group className="mb-3">
                            <Form.Label><strong>{t('new_password')}</strong></Form.Label>
                            <Form.Control
                                type="password"
                                value={newPassword}
                                onChange={(e) => setNewPassword(e.target.value)}
                                placeholder={t('enter_new_password')}
                                disabled={changePasswordLoading}
                                minLength={6}
                                required
                            />
                            <Form.Text className="text-muted">
                                {t('password_min_6_chars')}
                            </Form.Text>
                        </Form.Group>

                        <Form.Group className="mb-3">
                            <Form.Label><strong>{t('confirm_password')}</strong></Form.Label>
                            <Form.Control
                                type="password"
                                value={confirmPassword}
                                onChange={(e) => setConfirmPassword(e.target.value)}
                                placeholder={t('confirm_new_password')}
                                disabled={changePasswordLoading}
                                minLength={6}
                                required
                            />
                            <Form.Text className="text-muted">
                                {t('please_confirm_password')}
                            </Form.Text>
                        </Form.Group>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeChangePasswordModal} disabled={changePasswordLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleConfirmChangePassword}
                        disabled={changePasswordLoading || !newPassword || !confirmPassword}
                    >
                        {changePasswordLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('processing')}
                            </>
                        ) : (
                            <>
                                <FaKey className="me-1" />
                                {t('change_password')}
                            </>
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>

            {/* Cancel Member Modal */}
            <Modal show={showCancelMemberModal} onHide={closeCancelMemberModal} size="md">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('cancel_member')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closeCancelMemberModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {cancelMemberError && (
                        <Alert variant="danger" className="mb-3">
                            {cancelMemberError}
                        </Alert>
                    )}
                    {cancelMemberSuccess && (
                        <Alert variant="success" className="mb-3">
                            {cancelMemberSuccess}
                        </Alert>
                    )}

                    {cancelMemberTarget && (
                        <div className="mb-4">
                            <Card>
                                <Card.Header>
                                    <strong>{t('customer_info')}</strong>
                                </Card.Header>
                                <Card.Body>
                                    <p><strong>{t('username')}:</strong> {cancelMemberTarget.users?.email || '-'}</p>
                                    <p><strong>{t('real_name')}:</strong> {cancelMemberTarget.real_name || '-'}</p>
                                    <p><strong>{t('current_status')}:</strong> <StatusBadge status={cancelMemberTarget.verify_status} type="review" /></p>
                                </Card.Body>
                            </Card>
                        </div>
                    )}

                    <Alert variant="warning" className="mb-3">
                        <strong>{t('warning')}:</strong> {t('cancel_member_warning')}
                    </Alert>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeCancelMemberModal} disabled={cancelMemberLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="danger"
                        onClick={handleConfirmCancelMember}
                        disabled={cancelMemberLoading}
                    >
                        {cancelMemberLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('processing')}
                            </>
                        ) : (
                            <>
                                <FaUserTimes className="me-1" />
                                {t('confirm_cancel_member')}
                            </>
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default Members;