{"name": "frontend", "homepage": "./", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/x-data-grid": "^8.9.1", "@supabase/supabase-js": "^2.50.3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "ajv": "^8.17.1", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "bootstrap": "^5.3.7", "i18next": "^25.3.1", "qrcode.react": "^4.2.0", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-dom": "^19.1.0", "react-i18next": "^15.6.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.3", "react-scripts": "5.0.1", "recharts": "^3.0.2", "typescript": "4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"typescript": "4.9.5"}}