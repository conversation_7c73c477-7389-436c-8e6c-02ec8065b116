
import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, Badge, Modal, Form, Alert } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { getSupabase } from '../../supabaseClient';
import { FaTimes } from "react-icons/fa";

const ProductListPage = () => {
    const { t } = useTranslation();
    const [products, setProducts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [currentUser, setCurrentUser] = useState(null);

    // Purchase Modal states
    const [showPurchaseModal, setShowPurchaseModal] = useState(false);
    const [selectedProduct, setSelectedProduct] = useState(null);
    const [purchaseLoading, setPurchaseLoading] = useState(false);
    const [purchaseError, setPurchaseError] = useState('');
    const [purchaseSuccess, setPurchaseSuccess] = useState('');

    // Form data
    const [purchaseForm, setPurchaseForm] = useState({
        shares: '',
        proofImage: null
    });

    useEffect(() => {
        const fetchData = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);

            // Get current user
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                setLoading(false);
                return;
            }

            setCurrentUser(user);

            // Get user's customer profile to get agent_id
            const { data: customerProfile, error: profileError } = await supabase
                .from('customer_profiles')
                .select('agent_id')
                .eq('user_id', user.id)
                .single();

            if (profileError) {
                console.error('Error fetching customer profile:', profileError);
                setLoading(false);
                return;
            }

            // Fetch products
            const { data, error } = await supabase
                .from('products')
                .select(`
                    id,
                    name,
                    category,
                    price,
                    total_shares,
                    sold_shares,
                    effective_delay_days,
                    min_purchase,
                    is_disabled,
                    duration_days,
                    ops_commission_pct,
                    tech_commission_pct,
                    commission_agent_pct,
                    maker_profiles ( brand_name: user_id, maker_brand: domain )
                `)
                .eq('is_disabled', false)
                .eq('review_status', 'approved')
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching products:', error);
            } else {
                setProducts(data);
            }
            setLoading(false);
        };

        fetchData();
    }, []);

    const handlePurchase = (product) => {
        setSelectedProduct(product);
        setShowPurchaseModal(true);
        setPurchaseForm({
            shares: '',
            proofImage: null
        });
        setPurchaseError('');
        setPurchaseSuccess('');
    };

    const handleFormChange = (field, value) => {
        setPurchaseForm(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const validatePurchaseForm = () => {
        if (!purchaseForm.shares || parseFloat(purchaseForm.shares) <= 0) {
            setPurchaseError(t('shares_required'));
            return false;
        }

        const shares = parseFloat(purchaseForm.shares);
        const remainingShares = selectedProduct.total_shares - selectedProduct.sold_shares;

        if (shares < selectedProduct.min_purchase) {
            setPurchaseError(t('shares_below_minimum') + ': ' + selectedProduct.min_purchase);
            return false;
        }

        if (shares > remainingShares) {
            setPurchaseError(t('shares_exceed_available') + ': ' + remainingShares);
            return false;
        }

        if (!purchaseForm.proofImage) {
            setPurchaseError(t('proof_image_required'));
            return false;
        }

        return true;
    };

    const handleConfirmPurchase = async () => {
        if (!validatePurchaseForm()) {
            return;
        }

        setPurchaseLoading(true);
        setPurchaseError('');
        setPurchaseSuccess('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Get current user and customer profile
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                throw new Error('User not authenticated');
            }

            const { data: customerProfile, error: profileError } = await supabase
                .from('customer_profiles')
                .select('agent_id')
                .eq('user_id', user.id)
                .single();

            if (profileError || !customerProfile) {
                throw new Error('Customer profile not found');
            }

            // Upload proof image first
            const formData = new FormData();
            formData.append('proof_image', purchaseForm.proofImage);

            const uploadResponse = await fetch(`${window.wpData.apiUrl}upload-image`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-WP-Nonce': window.wpData.nonce
                }
            });

            const uploadResult = await uploadResponse.json();

            if (!uploadResult.success) {
                throw new Error(uploadResult.errors ? uploadResult.errors.join(', ') : 'Failed to upload image');
            }

            const proofImageUrl = uploadResult.files.proof_image.url;

            const startDate = new Date();
            const endDate = new Date();
            endDate.setDate(startDate.getDate() + selectedProduct.duration_days);

            // Create order
            const { data, error } = await supabase.from('orders').insert({
                product_id: selectedProduct.id,
                customer_id: user.id,
                agent_id: customerProfile.agent_id,
                shares: parseFloat(purchaseForm.shares),
                review_status: 'pending',
                proof_image_url: proofImageUrl,
                storage_cost: (selectedProduct.price * parseFloat(purchaseForm.shares)),
                pledge_cost: 0,
                total_rate: selectedProduct.tech_commission_pct + selectedProduct.ops_commission_pct + selectedProduct.commission_agent_pct,
                tech_fee_pct: selectedProduct.tech_commission_pct,
                sales_fee_pct: selectedProduct.commission_agent_pct,
                ops_fee_pct: selectedProduct.ops_commission_pct,
                start_at: startDate.toISOString().slice(0, 10),
                end_at: endDate.toISOString().slice(0, 10)
            });

            if (error) {
                throw error;
            }

            setPurchaseSuccess(t('order_created_successfully'));

            // Close modal after 2 seconds
            setTimeout(() => {
                setShowPurchaseModal(false);
            }, 2000);

        } catch (error) {
            console.error('Error creating order:', error);
            setPurchaseError(error.message || t('order_creation_error'));
        } finally {
            setPurchaseLoading(false);
        }
    };

    const closePurchaseModal = () => {
        setShowPurchaseModal(false);
        setPurchaseForm({
            shares: '',
            proofImage: null
        });
        setPurchaseError('');
        setPurchaseSuccess('');
    };

    if (loading) {
        return <div>{t('loading_products')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('power_products')}</h2>
            <Row>
                {products.map(product => (
                    <Col md={4} key={product.id} className="mb-4">
                        <Card className={product.is_disabled ? 'bg-light' : ''}>
                            <Card.Body>
                                <Card.Title className="d-flex justify-content-between">
                                    {product.name}
                                    <Badge bg="primary">{product.category}</Badge>
                                </Card.Title>
                                <Card.Subtitle className="mb-2 text-muted">{t('provided_by')} {product.maker_profiles?.maker_brand || t('official')}</Card.Subtitle>
                                <Card.Text className="mb-2">
                                    <strong>{t('price_label')}:</strong> {product.price} {t('usdt_per_share')} <br />
                                    <strong>{t('total_shares_label')}:</strong> {product.total_shares} <br />
                                    <strong>{t('remaining_shares_label')}:</strong> {product.total_shares - product.sold_shares} <br />
                                    <strong>{t('min_purchase_label')}:</strong> {product.min_purchase} {t('shares_unit')} <br />
                                    <strong>{t('waiting_period_label')}:</strong> {product.effective_delay_days} {t('days_unit')} <br />
                                    <strong>{t('duration_days')}:</strong> {product.duration_days} {t('days_unit')}
                                </Card.Text>
                                <Button
                                    variant="primary"
                                    disabled={product.is_disabled || (product.total_shares - product.sold_shares === 0)}
                                    onClick={() => handlePurchase(product)}
                                >
                                    {(product.total_shares - product.sold_shares === 0) ? t('sold_out') : t('buy_now')}
                                </Button>
                            </Card.Body>
                        </Card>
                    </Col>
                ))}
            </Row>

            {/* Purchase Modal */}
            <Modal show={showPurchaseModal} onHide={closePurchaseModal} size="lg">
                <Modal.Header closeButton={false} className="custom-modal-header d-flex justify-content-between">
                    <Modal.Title>{t('purchase_product')}</Modal.Title>
                    <Button
                        variant="outline-light"
                        className="btn-close-custom"
                        onClick={closePurchaseModal}
                        title="Close"
                    >
                        <FaTimes />
                    </Button>
                </Modal.Header>
                <Modal.Body>
                    {purchaseError && (
                        <Alert variant="danger" className="mb-3">
                            {purchaseError}
                        </Alert>
                    )}
                    {purchaseSuccess && (
                        <Alert variant="success" className="mb-3">
                            {purchaseSuccess}
                        </Alert>
                    )}

                    {selectedProduct && (
                        <div className="mb-3">
                            <h5>{selectedProduct.name}</h5>
                            <p><strong>{t('price_label')}:</strong> {selectedProduct.price} {t('usdt_per_share')}</p>
                            <p><strong>{t('remaining_shares_label')}:</strong> {selectedProduct.total_shares - selectedProduct.sold_shares}</p>
                            <p><strong>{t('min_purchase_label')}:</strong> {selectedProduct.min_purchase} {t('shares_unit')}</p>
                        </div>
                    )}

                    <Form>
                        <Form.Group className="mb-3">
                            <Form.Label><strong>{t('purchase_shares')}</strong></Form.Label>
                            <Form.Control
                                type="number"
                                value={purchaseForm.shares}
                                onChange={(e) => handleFormChange('shares', e.target.value)}
                                placeholder={t('enter_shares_amount')}
                                disabled={purchaseLoading}
                                min="1"
                                step="1"
                                required
                            />
                            {selectedProduct && (
                                <Form.Text className="text-muted">
                                    {t('min_purchase')}: {selectedProduct.min_purchase}, {t('max_available')}: {selectedProduct.total_shares - selectedProduct.sold_shares}
                                </Form.Text>
                            )}
                            {selectedProduct && purchaseForm.shares && (
                                <div className="mt-2">
                                    {t('total_price')}: {(parseFloat(purchaseForm.shares) * selectedProduct.price).toFixed(2)} {t('price_unit')}
                                </div>
                            )}

                        </Form.Group>

                        <Form.Group className="mb-3">
                            <Form.Label><strong>{t('proof_image')}</strong></Form.Label>
                            <Form.Control
                                type="file"
                                accept="image/*"
                                onChange={(e) => handleFormChange('proofImage', e.target.files[0])}
                                disabled={purchaseLoading}
                                required
                            />
                            <Form.Text className="text-muted">
                                {t('upload_payment_proof')}
                            </Form.Text>
                        </Form.Group>
                    </Form>
                    <Form.Text className="text-muted">
                        {t('payment_note')}
                    </Form.Text>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closePurchaseModal} disabled={purchaseLoading}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleConfirmPurchase}
                        disabled={purchaseLoading || !purchaseForm.shares || !purchaseForm.proofImage}
                    >
                        {purchaseLoading ? (
                            <>
                                <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                {t('processing')}
                            </>
                        ) : (
                            t('confirm_purchase')
                        )}
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default ProductListPage;
