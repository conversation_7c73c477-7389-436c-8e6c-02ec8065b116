import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { getSupabase, getCurrentMakerId } from '../../supabaseClient';

const MakerDashboard = () => {
    const { t } = useTranslation();
    const [makerProfile, setMakerProfile] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchMakerData = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);

            // Get maker ID (works for both maker and technician roles)
            const makerId = await getCurrentMakerId();
            if (!makerId) {
                setLoading(false);
                return; // User not authenticated or not authorized
            }

            // Fetch maker profile
            const { data: profileData, error: profileError } = await supabase
                .from('maker_profiles')
                .select('*')
                .eq('user_id', makerId)
                .single();

            if (profileError) {
                console.error('Error fetching maker profile:', profileError);
            } else {
                setMakerProfile(profileData);
            }
            setLoading(false);
        };

        fetchMakerData();
    }, []);

    if (loading) {
        return <div>{t('loading_maker_dashboard')}</div>;
    }

    if (!makerProfile) {
        return <div className="alert alert-warning">{t('not_maker')}</div>;
    }

    return (
    <div style={{ display: "flex" }}>
      <div style={{ margin: 4, flex: 1, width: "100%" }}>
        <Container fluid>
            <Row className="mb-3">
                <Col>
                    <h3>{t('maker_dashboard')}</h3>
                </Col>
            </Row>

            <Row>
                <Col md={4}>
                    <Card className="card-box card-primary mb-3">
                        <Card.Body>
                            <Card.Title>{t('domain')}</Card.Title>
                            <h3>{makerProfile.domain || 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card className="card-box card-success mb-3">
                        <Card.Body>
                            <Card.Title>{t('support_email')}</Card.Title>
                            <h3>{makerProfile.support_email || 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={4}>
                    <Card className="card-box card-info mb-3">
                        <Card.Body>
                            <Card.Title>{t('sms_signature')}</Card.Title>
                            <h3>{makerProfile.sms_signature || 'N/A'}</h3>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row className="mt-4">
                <Col md={6} className="text-center">
                    <Card className="card-box card-primary">
                        <Card.Body>
                            <h4>{t('product_management')}</h4>
                            <p>{t('manage_your_products')}</p>
                            <Button as={Link} classname="mt-3" to="/maker/products">{t('enter_product_list')}</Button>
                        </Card.Body>
                    </Card>
                </Col>
                <Col md={6} className="text-center">
                    <Card className="card-box card-success">
                        <Card.Body>
                            <h4>{t('order_management')}</h4>
                            <p>{t('all_orders')}</p>
                            <Button as={Link} classname="mt-3" to="/maker/orders">{t('enter_order_list')}</Button>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
      </div>
    </div>
    );
};

export default MakerDashboard;
