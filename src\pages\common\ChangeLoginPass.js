import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert } from 'react-bootstrap';
import { useTranslation } from 'react-i18next';
import { getSupabase } from '../../supabaseClient';
import { useNavigate } from 'react-router-dom';
import { FaEye, FaEyeSlash } from 'react-icons/fa';

const ChangeLoginPass = () => {
    const { t } = useTranslation();
    const [currentPassword, setCurrentPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const [message, setMessage] = useState('');
    const [error, setError] = useState('');
    const [userRole, setUserRole] = useState('');
    const [showCurrentPassword, setShowCurrentPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const navigate = useNavigate();

    // 获取用户角色以确定跳转路径
    useEffect(() => {
        const role = localStorage.getItem('user_role');
        setUserRole(role || 'customer');
    }, []);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setMessage('');

        // 验证新密码和确认密码是否一致
        if (newPassword !== confirmPassword) {
            setError(t('passwords_do_not_match'));
            return;
        }

        // 验证新密码长度
        if (newPassword.length < 6) {
            setError(t('password_too_short'));
            return;
        }

        setLoading(true);

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error(t('backend_connection_failed'));
            }

            // 获取当前用户
            const { data: { user }, error: userError } = await supabase.auth.getUser();
            if (userError || !user) {
                throw new Error(t('user_not_authenticated'));
            }

            // 首先验证当前密码是否正确
            // 通过重新登录来验证当前密码
            const { error: signInError } = await supabase.auth.signInWithPassword({
                email: user.email,
                password: currentPassword,
            });

            if (signInError) {
                throw new Error(t('current_password_incorrect'));
            }

            // 更新密码
            const { error: updateError } = await supabase.auth.updateUser({
                password: newPassword
            });

            if (updateError) {
                throw updateError;
            }

            setMessage(t('password_updated_successfully'));
            
            // 清空表单
            setCurrentPassword('');
            setNewPassword('');
            setConfirmPassword('');

            // 3秒后根据用户角色跳转到相应页面
            setTimeout(() => {
                switch (userRole) {
                    case 'maker':
                        navigate('/maker');
                        break;
                    case 'agent':
                        navigate('/agent');
                        break;
                    case 'customer':
                    default:
                        navigate('/my');
                        break;
                }
            }, 3000);

        } catch (err) {
            console.error('Password update error:', err);
            setError(err.message || t('password_update_failed'));
        }

        setLoading(false);
    };

    return (
        <Container>
            <Row className="justify-content-center">
                <Col md={6}>
                    <Card>
                        <Card.Header>
                            <h4 className="mb-0">{t('change_login_password')}</h4>
                        </Card.Header>
                        <Card.Body>
                            {error && <Alert variant="danger">{error}</Alert>}
                            {message && <Alert variant="success">{message}</Alert>}
                            
                            <Form onSubmit={handleSubmit}>
                                <Form.Group className="mb-3">
                                    <Form.Label><span className="text-danger">*</span>{t('current_password')}</Form.Label>
                                    <div style={{ position: 'relative' }}>
                                        <Form.Control
                                            type={showCurrentPassword ? "text" : "password"}
                                            value={currentPassword}
                                            onChange={(e) => setCurrentPassword(e.target.value)}
                                            required
                                            placeholder={t('enter_current_password')}
                                            style={{ paddingRight: '40px' }}
                                        />
                                        <span
                                            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                            style={{
                                                position: 'absolute',
                                                right: '12px',
                                                top: '50%',
                                                transform: 'translateY(-50%)',
                                                cursor: 'pointer',
                                                color: '#6c757d',
                                                fontSize: '16px'
                                            }}
                                        >
                                            {showCurrentPassword ? <FaEyeSlash /> : <FaEye />}
                                        </span>
                                    </div>
                                </Form.Group>

                                <Form.Group className="mb-3">
                                    <Form.Label><span className="text-danger">*</span>{t('new_password')}</Form.Label>
                                    <div style={{ position: 'relative' }}>
                                        <Form.Control
                                            type={showNewPassword ? "text" : "password"}
                                            value={newPassword}
                                            onChange={(e) => setNewPassword(e.target.value)}
                                            required
                                            minLength={6}
                                            placeholder={t('enter_new_password')}
                                            style={{ paddingRight: '40px' }}
                                        />
                                        <span
                                            onClick={() => setShowNewPassword(!showNewPassword)}
                                            style={{
                                                position: 'absolute',
                                                right: '12px',
                                                top: '50%',
                                                transform: 'translateY(-50%)',
                                                cursor: 'pointer',
                                                color: '#6c757d',
                                                fontSize: '16px'
                                            }}
                                        >
                                            {showNewPassword ? <FaEyeSlash /> : <FaEye />}
                                        </span>
                                    </div>
                                    <Form.Text className="text-muted">
                                        {t('password_requirements')}
                                    </Form.Text>
                                </Form.Group>

                                <Form.Group className="mb-3">
                                    <Form.Label><span className="text-danger">*</span>{t('confirm_new_password')}</Form.Label>
                                    <div style={{ position: 'relative' }}>
                                        <Form.Control
                                            type={showConfirmPassword ? "text" : "password"}
                                            value={confirmPassword}
                                            onChange={(e) => setConfirmPassword(e.target.value)}
                                            required
                                            minLength={6}
                                            placeholder={t('confirm_new_password')}
                                            style={{ paddingRight: '40px' }}
                                        />
                                        <span
                                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                            style={{
                                                position: 'absolute',
                                                right: '12px',
                                                top: '50%',
                                                transform: 'translateY(-50%)',
                                                cursor: 'pointer',
                                                color: '#6c757d',
                                                fontSize: '16px'
                                            }}
                                        >
                                            {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                                        </span>
                                    </div>
                                </Form.Group>

                                <div className="d-grid gap-2">
                                    <Button 
                                        variant="primary" 
                                        type="submit" 
                                        disabled={loading}
                                    >
                                        {loading ? t('updating') : t('update_password')}
                                    </Button>
                                    <Button 
                                        variant="secondary" 
                                        onClick={() => {
                                            switch (userRole) {
                                                case 'maker':
                                                    navigate('/maker');
                                                    break;
                                                case 'agent':
                                                    navigate('/agent');
                                                    break;
                                                case 'customer':
                                                default:
                                                    navigate('/my');
                                                    break;
                                            }
                                        }}
                                        disabled={loading}
                                    >
                                        {t('cancel')}
                                    </Button>
                                </div>
                            </Form>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default ChangeLoginPass;
