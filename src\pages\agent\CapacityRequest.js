import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Table, Button, Modal, Form, Alert, InputGroup } from 'react-bootstrap';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import { FaPlus, FaDownload } from "react-icons/fa";
import StatusBadge from '../../components/StatusBadge';

const CapacityRequest = () => {
    const { t } = useTranslation();
    const [requests, setRequests] = useState([]);
    const [loading, setLoading] = useState(true);
    const [products, setProducts] = useState([]);

    // Add Capacity Request Modal states
    const [showAddRequestModal, setShowAddRequestModal] = useState(false);
    const [addRequestLoading, setAddRequestLoading] = useState(false);
    const [addRequestError, setAddRequestError] = useState('');
    const [addRequestSuccess, setAddRequestSuccess] = useState('');

    // Form data
    const [requestForm, setRequestForm] = useState({
        product_id: '',
        added_capacity: '',
        description: ''
    });

    useEffect(() => {
        const fetchData = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            // First, get the agent's maker_id
            const { data: agentProfile, error: agentError } = await supabase
                .from('agent_profiles')
                .select('maker_id')
                .eq('user_id', user.id)
                .single();

            if (agentError) {
                console.error('Error fetching agent profile:', agentError);
                setLoading(false);
                return;
            }

            if (!agentProfile || !agentProfile.maker_id) {
                console.error('Agent profile not found or no maker_id');
                setLoading(false);
                return;
            }

            // Fetch products for this agent's maker
            const { data: productsData, error: productsError } = await supabase
                .from('products')
                .select('id, name, total_shares, sold_shares')
                .eq('agent_id', user.id)
                .eq('is_disabled', false)
                .order('name');

            if (productsError) {
                console.error('Error fetching products:', productsError);
            } else {
                setProducts(productsData);
            }

            // Fetch capacity requests for this agent's maker only
            const { data, error } = await supabase
                .from('capacity_requests')
                .select(`
                    id,
                    product_id,
                    added_capacity,
                    capacity_before,
                    capacity_after,
                    status,
                    description,
                    review_reply,
                    requested_at,
                    reviewed_at,
                    products (
                        name
                    ),
                    maker_profiles (
                        domain,
                        users (
                            email
                        )
                    ),
                    users (
                        email,
                        role
                    )
                `)
                .eq('maker_id', agentProfile.maker_id)
                .order('requested_at', { ascending: false });

            if (error) {
                console.error('Error fetching capacity requests:', error);
            } else {
                setRequests(data);
            }
            setLoading(false);
        };

        fetchData();
    }, []);

    // Export requests to CSV
    const exportToCSV = () => {
        if (requests.length === 0) {
            alert(t('no_data_to_export'));
            return;
        }

        // Define CSV headers
        const headers = [
            t('request_id'),
            t('maker'),
            t('requested_by'),
            t('product_name'),
            t('added_capacity'),
            t('capacity_before'),
            t('capacity_after'),
            t('status'),
            t('description'),
            t('review_reply'),
            t('requested_at'),
            t('reviewed_at')
        ];

        // Convert data to CSV format
        const csvData = requests.map(request => [
            request.id,
            request.maker_profiles?.domain || '-',
            request.users?.email || '-',
            request.products?.name || '-',
            request.added_capacity?.toFixed(2) || '0.00',
            request.capacity_before?.toFixed(2) || '0.00',
            request.capacity_after?.toFixed(2) || '0.00',
            t(request.status) || 'pending',
            request.description || '-',
            request.review_reply || '-',
            new Date(request.requested_at).toLocaleString(),
            request.reviewed_at ? new Date(request.reviewed_at).toLocaleString() : '-'
        ]);

        // Combine headers and data
        const csvContent = [headers, ...csvData]
            .map(row => row.map(field => `"${field}"`).join(','))
            .join('\n');

        // Add UTF-8 BOM to ensure proper encoding for Japanese characters
        const BOM = '\uFEFF';
        const csvWithBOM = BOM + csvContent;

        // Create and download CSV file with proper UTF-8 encoding
        const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `agent_capacity_requests_${new Date().toISOString().split('T')[0]}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleAddRequest = () => {
        setShowAddRequestModal(true);
        setRequestForm({
            product_id: '',
            added_capacity: '',
            description: ''
        });
        setAddRequestError('');
        setAddRequestSuccess('');
    };

    const handleFormChange = (field, value) => {
        setRequestForm(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleSubmitRequest = async () => {
        if (!requestForm.product_id || !requestForm.added_capacity) {
            setAddRequestError(t('please_fill_required_fields'));
            return;
        }

        setAddRequestLoading(true);
        setAddRequestError('');

        try {
            const supabase = getSupabase();
            if (!supabase) {
                throw new Error('Database connection failed');
            }

            // Get current user
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) {
                throw new Error('User not authenticated');
            }

            // Get agent profile to find maker_id
            const { data: agentProfile, error: agentError } = await supabase
                .from('agent_profiles')
                .select('maker_id')
                .eq('user_id', user.id)
                .single();

            if (agentError || !agentProfile) {
                throw new Error('Agent profile not found');
            }

            // Get current product data
            const { data: productData, error: productError } = await supabase
                .from('products')
                .select('total_shares')
                .eq('id', requestForm.product_id)
                .single();

            if (productError || !productData) {
                throw new Error('Product not found');
            }

            const addedCapacity = parseFloat(requestForm.added_capacity);
            const capacityBefore = productData.total_shares;
            const capacityAfter = capacityBefore + addedCapacity;

            // Insert capacity request
            const { error: insertError } = await supabase
                .from('capacity_requests')
                .insert({
                    maker_id: agentProfile.maker_id,
                    product_id: requestForm.product_id,
                    added_capacity: addedCapacity,
                    capacity_before: capacityBefore,
                    capacity_after: capacityAfter,
                    requested_by: user.id,
                    status: 'pending',
                    description: requestForm.description
                });

            if (insertError) {
                throw insertError;
            }

            setAddRequestSuccess(t('capacity_request_submitted_successfully'));

            // Refresh data
            const fetchData = async () => {
                // Fetch capacity requests for this agent's maker only
                const { data, error } = await supabase
                    .from('capacity_requests')
                    .select(`
                        id,
                        product_id,
                        added_capacity,
                        capacity_before,
                        capacity_after,
                        status,
                        description,
                        review_reply,
                        requested_at,
                        reviewed_at,
                        products (
                            name
                        ),
                        maker_profiles (
                            domain,
                            users (
                                email
                            )
                        ),
                        users (
                            email,
                            role
                        )
                    `)
                    .eq('maker_id', agentProfile.maker_id)
                    .order('requested_at', { ascending: false });

                if (!error) {
                    setRequests(data);
                }
            };

            fetchData();

            // Close modal after 2 seconds
            setTimeout(() => {
                setShowAddRequestModal(false);
            }, 2000);

        } catch (error) {
            console.error('Error creating capacity request:', error);
            setAddRequestError(error.message || t('capacity_request_creation_error'));
        } finally {
            setAddRequestLoading(false);
        }
    };

    const closeAddRequestModal = () => {
        setShowAddRequestModal(false);
        setRequestForm({
            product_id: '',
            added_capacity: '',
            description: ''
        });
        setAddRequestError('');
        setAddRequestSuccess('');
    };

    if (loading) {
        return <div>{t('loading_capacity_requests')}</div>;
    }

    return (
        <Container>
            <h2 className="mb-4">{t('capacity_expansion_request')}</h2>

            {/* Top Operation Bar */}
            <Row className="mb-4">
                <Col>
                    <Card>
                        <Card.Body>
                            <Row className="align-items-center">
                                <Col>
                                    <div className="d-flex gap-2">
                                        <Button
                                            variant="success"
                                            onClick={exportToCSV}
                                            disabled={requests.length === 0}
                                        >
                                            <FaDownload className="me-1" />
                                            {t('export_all')}
                                        </Button>
                                        <Button
                                            variant="primary"
                                            onClick={handleAddRequest}
                                        >
                                            <FaPlus className="me-1" />
                                            {t('add_capacity_request')}
                                        </Button>
                                    </div>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            <Row>
                <Col>
                    <Card>
                        <Card.Body>
                            <Table striped bordered hover responsive>
                                <thead>
                                    <tr>
                                        <th>{t('request_id')}</th>
                                        <th>{t('maker')}</th>
                                        <th>{t('requested_by')}</th>
                                        <th>{t('product_name')}</th>
                                        <th>{t('added_capacity')}</th>
                                        <th>{t('capacity_before')}</th>
                                        <th>{t('capacity_after')}</th>
                                        <th>{t('status')}</th>
                                        <th>{t('description')}</th>
                                        <th>{t('review_reply')}</th>
                                        <th>{t('requested_at')}</th>
                                        <th>{t('reviewed_at')}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {requests.length === 0 ? (
                                        <tr>
                                            <td colSpan="12" className="text-center">{t('no_capacity_requests_available')}</td>
                                        </tr>
                                    ) : (
                                        requests.map(request => (
                                            <tr key={request.id}>
                                                <td>{request.id}</td>
                                                <td>
                                                    <div>
                                                        <div>{request.maker_profiles?.domain || '-'}</div>
                                                        <small className="text-muted">
                                                            {request.maker_profiles?.users?.email || '-'}
                                                        </small>
                                                    </div>
                                                </td>
                                                <td>{request.users?.email || '-'}</td>
                                                <td>{request.products?.name || '-'}</td>
                                                <td>{request.added_capacity?.toFixed(2) || '0.00'}</td>
                                                <td>{request.capacity_before?.toFixed(2) || '0.00'}</td>
                                                <td>{request.capacity_after?.toFixed(2) || '0.00'}</td>
                                                <td><StatusBadge status={request.status} type="review" /></td>
                                                <td>
                                                    <div style={{ maxWidth: '200px', wordWrap: 'break-word' }}>
                                                        {request.description || '-'}
                                                    </div>
                                                </td>
                                                <td>
                                                    <div style={{ maxWidth: '200px', wordWrap: 'break-word' }}>
                                                        {request.review_reply || '-'}
                                                    </div>
                                                </td>
                                                <td>{new Date(request.requested_at).toLocaleString()}</td>
                                                <td>{request.reviewed_at ? new Date(request.reviewed_at).toLocaleString() : '-'}</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </Table>
                        </Card.Body>
                    </Card>
                </Col>
            </Row>

            {/* Add Capacity Request Modal */}
            <Modal show={showAddRequestModal} onHide={closeAddRequestModal} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>{t('add_capacity_request')}</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    {addRequestError && <Alert variant="danger">{addRequestError}</Alert>}
                    {addRequestSuccess && <Alert variant="success">{addRequestSuccess}</Alert>}

                    <Form>
                        <Row>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>{t('product')} *</Form.Label>
                                    <Form.Select
                                        value={requestForm.product_id}
                                        onChange={(e) => handleFormChange('product_id', e.target.value)}
                                        required
                                    >
                                        <option value="">{t('select_product')}</option>
                                        {products.map(product => (
                                            <option key={product.id} value={product.id}>
                                                {product.name} ({product.total_shares - product.sold_shares} {t('remaining_shares')})
                                            </option>
                                        ))}
                                    </Form.Select>
                                </Form.Group>
                            </Col>
                            <Col md={6}>
                                <Form.Group className="mb-3">
                                    <Form.Label>{t('added_capacity')} *</Form.Label>
                                    <InputGroup>
                                        <Form.Control
                                            type="number"
                                            step="0.01"
                                            min="0"
                                            value={requestForm.added_capacity}
                                            onChange={(e) => handleFormChange('added_capacity', e.target.value)}
                                            placeholder={t('enter_added_capacity')}
                                            required
                                        />
                                        <InputGroup.Text>{t('shares')}</InputGroup.Text>
                                    </InputGroup>
                                </Form.Group>
                            </Col>
                        </Row>

                        <Form.Group className="mb-3">
                            <Form.Label>{t('description')}</Form.Label>
                            <Form.Control
                                as="textarea"
                                rows={3}
                                value={requestForm.description}
                                onChange={(e) => handleFormChange('description', e.target.value)}
                                placeholder={t('enter_description')}
                            />
                        </Form.Group>
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={closeAddRequestModal}>
                        {t('cancel')}
                    </Button>
                    <Button
                        variant="primary"
                        onClick={handleSubmitRequest}
                        disabled={addRequestLoading}
                    >
                        {addRequestLoading ? t('submitting') : t('submit')}
                    </Button>
                </Modal.Footer>
            </Modal>
        </Container>
    );
};

export default CapacityRequest;