import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Container, Row, Col, Card, Table, Spinner, Al<PERSON>, But<PERSON> } from 'react-bootstrap';
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { getSupabase } from '../../supabaseClient';

const StatCard = ({ title, value, unit, loading, icon }) => (
    <Card className={`mb-3 h-100`} style={{ backgroundColor: '#F0F6FB' }}>
        <Card.Body className="d-flex flex-column justify-content-between">
            <div className="d-flex justify-content-between align-items-start">
                <div>
                    <Card.Title className="h6">{title}</Card.Title>
                    {loading ? (
                        <div className="d-flex align-items-center">
                            <Spinner animation="border" size="sm" className="me-2" />
                            <span>Loading...</span>
                        </div>
                    ) : (
                        <div>
                            <h4 className="mb-0">{value}</h4>
                            {unit && <small className="opacity-75">{unit}</small>}
                        </div>
                    )}
                </div>
                {icon && <div className="fs-2 opacity-50">{icon}</div>}
            </div>
        </Card.Body>
    </Card>
);

const Filfox = () => {
    const { t } = useTranslation();
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [currentStats, setCurrentStats] = useState(null);
    const [historicalData, setHistoricalData] = useState([]);
    const [refreshing, setRefreshing] = useState(false);

    // Fetch real-time network stats from WordPress API
    const fetchNetworkStats = async () => {
        try {
            setLoading(true);

            // First test the API connection
            const testUrl = window.location.origin + '/wp-json/fil-platform/v1/test';

            const testResponse = await fetch(testUrl, {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (testResponse.ok) {
                const testResult = await testResponse.json();
            } else {
                console.error('Test API failed:', testResponse.status);
            }

            // Now try the real endpoint
            const wpApiUrl = window.location.origin + '/wp-json/fil-platform/v1/filfox-realtime';

            const response = await fetch(wpApiUrl, {
                method: 'GET',
                credentials: 'include', // Include cookies for authentication
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Error response:', errorText);
                throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
            }

            const result = await response.json();

            if (result.success) {
                setCurrentStats(result.data);
                setError(null);
            } else {
                setError(result.message || 'Failed to fetch real-time data');
            }

            // For historical data, we'll fetch from Supabase (only fil_per_tib)
            await fetchHistoricalData();

        } catch (error) {
            console.error('Error fetching real-time stats:', error);
            setError('Failed to load real-time network statistics: ' + error.message);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    // Fetch historical data for charts (only fil_per_tib from database)
    const fetchHistoricalData = async () => {
        const supabase = getSupabase();
        if (!supabase) return;

        try {
            const { data: { user } } = await supabase.auth.getUser();
            if (!user) return;

            // Fetch historical data for charts (last 30 days, only fil_per_tib)
            const { data: historicalData, error: historicalError } = await supabase
                .from('network_stats')
                .select('stat_date, fil_per_tib')
                .order('stat_date', { ascending: false })
                .limit(30);

            if (historicalError) {
                console.error('Error fetching historical stats:', historicalError);
            } else {
                // Reverse to show chronological order in charts
                setHistoricalData(historicalData.reverse());
            }
        } catch (error) {
            console.error('Error fetching historical data:', error);
        }
    };

    useEffect(() => {
        fetchNetworkStats();
    }, []);

    const handleRefresh = () => {
        setRefreshing(true);
        fetchNetworkStats();
    };

    const formatNumber = (num) => {
        if (num === null || num === undefined) return 'N/A';
        return new Intl.NumberFormat('en-US', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 4
        }).format(num);
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString();
    };

    if (error) {
        return (
            <Container fluid>
                <Row className="mb-3">
                    <Col>
                        <h2>{t('filfox_network_stats')}</h2>
                        <Alert>{error}</Alert>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container fluid>
            <Row className="mb-3">
                <Col>
                    <div className="d-flex justify-content-between align-items-center">
                        <h2>🌐{t('filfox_network_stats')}</h2>
                        <Button 
                            onClick={handleRefresh}
                            disabled={refreshing}
                        >
                            {refreshing ? (
                                <>
                                    <Spinner animation="border" size="sm" className="me-2" />
                                    {t('refreshing')}
                                </>
                            ) : (
                                t('refresh')
                            )}
                        </Button>
                    </div>
                </Col>
            </Row>

            {/* Current Statistics Cards */}
            <Row className="mb-4">
                <Col md={3}>
                    <StatCard
                        title={t('block_height')}
                        value={formatNumber(currentStats?.block_height)}
                        loading={loading}
                        icon="🔗"
                    />
                </Col>
                <Col md={3}>
                    <StatCard
                        title={t('network_storage_power')}
                        value={formatNumber(currentStats?.network_storage_power)}
                        unit="EiB"
                        loading={loading}
                        icon="💾"
                    />
                </Col>
                <Col md={3}>
                    <StatCard
                        title={t('active_miners')}
                        value={formatNumber(currentStats?.active_miners)}
                        loading={loading}
                        icon="⛏️"
                    />
                </Col>
                <Col md={3}>
                    <StatCard
                        title={t('block_reward')}
                        value={formatNumber(currentStats?.block_reward)}
                        unit="FIL"
                        loading={loading}
                        icon="🎁"
                    />
                </Col>
            </Row>

            <Row className="mb-4">
                <Col md={3}>
                    <StatCard
                        title={t('mining_reward_24h')}
                        value={formatNumber(currentStats?.mining_reward)}
                        unit="FIL/TiB"
                        loading={loading}
                        icon="⚡"
                    />
                </Col>
                <Col md={3}>
                    <StatCard
                        title={t('fil_production_24h')}
                        value={formatNumber(currentStats?.fil_production_24h)}
                        unit="FIL"
                        loading={loading}
                        icon="🔄"
                    />
                </Col>
                <Col md={3}>
                    <StatCard
                        title={t('total_pledge_collateral')}
                        value={formatNumber(currentStats?.total_pledge_collateral)}
                        unit="FIL"
                        loading={loading}
                        icon="🔒"
                    />
                </Col>
                <Col md={3}>
                    <StatCard
                        title={t('messages_24h')}
                        value={formatNumber(currentStats?.messages_24h)}
                        loading={loading}
                        icon="📨"
                    />
                </Col>
            </Row>

            {/* Additional Stats */}
            <Row className="mb-4">
                <Col md={6}>
                    <StatCard
                        title={t('sector_initial_pledge')}
                        value={formatNumber(currentStats?.sector_initial_pledge)}
                        unit="FIL/32GiB"
                        loading={loading}
                        icon="🔐"
                    />
                </Col>
                <Col md={6}>
                    <StatCard
                        title={t('latest_block')}
                        value={currentStats?.latest_block || 'N/A'}
                        loading={loading}
                        icon="⏰"
                    />
                </Col>
            </Row>

            {/* Current Data Summary */}
            <Row>
                <Col>
                    <Card>
                        <Card.Body style={{ backgroundColor: '#F0F6FB' }}>
                            <Card.Title>📖{t('current_network_summary')}</Card.Title>
                            {loading ? (
                                <div className="text-center">
                                    <Spinner animation="border" />
                                    <p className="mt-2">{t('loading')}</p>
                                </div>
                            ) : currentStats ? (
                                <Table striped bordered hover responsive className="blue-striped">
                                    <tbody>
                                        <tr>
                                            <td><strong>{t('block_height')}</strong></td>
                                            <td>{formatNumber(currentStats.block_height)}</td>
                                            <td><strong>{t('network_storage_power')}</strong></td>
                                            <td>{formatNumber(currentStats.network_storage_power)} EiB</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{t('active_miners')}</strong></td>
                                            <td>{formatNumber(currentStats.active_miners)}</td>
                                            <td><strong>{t('block_reward')}</strong></td>
                                            <td>{formatNumber(currentStats.block_reward)} FIL</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{t('mining_reward_24h')}</strong></td>
                                            <td>{formatNumber(currentStats.mining_reward)} FIL/TiB</td>
                                            <td><strong>{t('fil_production_24h')}</strong></td>
                                            <td>{formatNumber(currentStats.fil_production_24h)} FIL</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{t('total_pledge_collateral')}</strong></td>
                                            <td>{formatNumber(currentStats.total_pledge_collateral)} FIL</td>
                                            <td><strong>{t('messages_24h')}</strong></td>
                                            <td>{formatNumber(currentStats.messages_24h)}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{t('sector_initial_pledge')}</strong></td>
                                            <td>{formatNumber(currentStats.sector_initial_pledge)} FIL/32GiB</td>
                                            <td><strong>{t('latest_block')}</strong></td>
                                            <td>{currentStats.latest_block || 'N/A'}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>{t('last_updated')}</strong></td>
                                            <td colSpan="3">{currentStats.scraped_at ? new Date(currentStats.scraped_at).toLocaleString() : 'N/A'}</td>
                                        </tr>
                                    </tbody>
                                </Table>
                            ) : (
                                <p>{t('no_data_available')}</p>
                            )}
                        </Card.Body>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default Filfox;
